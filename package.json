{"name": "nim", "version": "0.1.0", "private": true, "engines": {"node": "^18.18.0 || ^19.8.0 || >= 20.0.0"}, "scripts": {"dev": "npm run generate:work && npm run generate:dev-images && next dev", "generate:dev-images": "node scripts/generate-dev-images.js", "build": "npm run encrypt:content && npm run generate:work && npm run build:next && npm run img", "build:next": "NODE_OPTIONS='--max_old_space_size=4096' next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "generate:work": "node scripts/generate-work-data.js", "encrypt:content": "node scripts/encrypt-content.js", "img": "node scripts/optimize-images.js", "clean": "rm -rf .next out .image-cache.json", "clean:images": "rm -rf public/images", "preview": "npm run build && npm run start", "analyze": "ANALYZE=true npm run build:next"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mdx-js/loader": "^3.0.1", "@mdx-js/react": "^3.0.1", "@next/mdx": "15.3.0", "@tailwindcss/typography": "^0.5.15", "@types/mdx": "^2.0.13", "clsx": "^2.1.1", "gray-matter": "^4.0.3", "html-react-parser": "^5.2.3", "lucide-react": "^0.468.0", "mdast": "^2.3.2", "mixpanel-browser": "^2.65.0", "motion": "^12.16.0", "next": "15.3.0", "react": "19.1.0", "react-dom": "19.1.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "server-only": "^0.0.1", "sharp": "^0.34.1", "tailwind-merge": "^2.5.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.0.0", "@types/mdast": "^4.0.4", "@types/mixpanel-browser": "^2.60.0", "@types/node": "^20", "@types/react": "19.1.1", "@types/react-dom": "19.1.2", "eslint": "^9", "eslint-config-next": "15.3.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.10", "tailwindcss": "^4.0.0", "typescript": "^5"}, "overrides": {"@types/react": "19.1.1", "@types/react-dom": "19.1.2"}}