import type { MDXComponents } from 'mdx/types'

export function useMDXComponents(components: MDXComponents): MDXComponents {
  return {
    ...components,
    Cover: ({
      src,
      alt,
      caption,
    }: {
      src: string
      alt: string
      caption: string
    }) => {
      return (
        <figure className="m-0">
          <img
            src={src}
            alt={alt}
            className="transition-all duration-500 ease-out rounded-sm"
          />
          <figcaption className="text-center">{caption}</figcaption>
        </figure>
      )
    },
  }
}
