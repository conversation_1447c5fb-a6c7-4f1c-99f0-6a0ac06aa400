---
title: 'Carousel Component Demo'
subtitle: 'Figma Sites-style carousel showcase'
date: '2025-01-15'
coverImage: '/images/thumb_orgvue.jpg'
showOnHomepage: false
---

# Carousel Component Demo

This document demonstrates the new WorkCarousel component that matches the exact look and functionality of the Figma Sites carousel.

## Figma Sites-style Carousel

The carousel shows 2 full images and a partial third image, with navigation arrows and a slide counter, exactly like the one on the Figma Sites webpage.

!carousel[/images/work_parknshop_cover.jpg|PARKnSHOP Interface|Pre-built interactions help you bring motion to your designs|/images/work_elevate_1.jpg|Elevate Dashboard|View a fully responsive site before publishing, just like you'd see in a Figma prototype|/images/work_harrisonmediahub_1.jpg|Harrison MediaHub|Ready-to-use blocks help you get started without having to design from scratch|/images/work_pns_ds_1.jpg|Design System|Coming soon, code layers will let you turn designs into interactive experiences]

## Features

The carousel component includes:

- **2.2 image layout**: Shows 2 full images and a partial third image
- **Navigation controls**: Arrow buttons on the top right
- **Slide counter**: Shows current position (e.g., "1 / 4") on the top left
- **Smooth animations**: 300ms transition between slides
- **Responsive design**: Adapts to different screen sizes
- **Dark mode support**: Automatically adjusts for light/dark themes
- **Optional captions**: Each image can have a caption overlay
- **Disabled states**: Navigation arrows are disabled at start/end

## Usage

To use the carousel in your work posts, use the following syntax:

```md
!carousel[image1.jpg|Alt text 1|Caption 1|image2.jpg|Alt text 2|Caption 2|image3.jpg|Alt text 3|Caption 3]
```

The pattern is: `src|alt|caption` for each image, separated by pipes. Captions are optional - you can leave them empty but still need the pipe separator.

## Multiple Carousels

You can have multiple carousels in the same post:

!carousel[/images/work_citysuper_cover.jpg|city'super App|Mobile shopping experience|/images/work_orgvue_1.jpg|OrgVue Platform|Data visualization dashboard|/images/work_onenetwork_cover.jpg|OneNetwork Web|E-commerce platform]

Each carousel operates independently with its own navigation and state.
