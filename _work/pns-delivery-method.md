---
title: 'Improving delivery method selection'
subtitle: 'PARKnSHOP'
date: '2023-09-17'
coverImage: '/images/thumb_parknshop.jpg'
showOnHomepage: false
---

![Cover (wide)](/images/work_pnsdelivery_cover.png)

!worksummary[Platfrom: App|Team: Solo designer \
1 Product manager \
Engineer team
]

### Problem

# Customers abandon cart when products out of stock after adding delivery address at checkout.

In our customer feedback, we saw first time app users (not logged in) complained about products being out of stock after adding delivery address at checkout.

This problem exist because product availability is determined by district (Home delivery) and individual store (Pickup). If the users switch their district or store at checkout it may affect stock availability.

![Cover (mid)](/images/work_pnsdelivery_flowold.png)

---

### Process

# Quickly understand, ideate and validate designs

1. **Understand**
2. **Ideate**
3. **Test with users**
4. **Iterate**
5. **Handoff**

---

### Understand and Ideate

# Balancing technical effort and user needs

I first analysed the flow and communicated with the tech team to determine what could be modified.

I have thought about making it compulsory for every user to enter the delivery method and details. However, in some usecases it does not involve entering the address (search for a product to see which store it is at) and we would like to keep the friction as low as possible.

To balance friction and the severity of the problem, I made the prompt appear when guest users first open the app while making it optional.

GPS functionality is not implemented because of the high technical effort involved.

---

### Test with users

# Rapidly prototype and test with users

I created a prototype and tested with 5 users (from our reward testing program). Each user was given 2 scenarios, Below were the feedbacks of the user testing:

![User Test (mid)](/images/work_pnsdelivery_usertest1.png)

With the user testing results, I am able to push for implementation of GPS functionality as it is a vital part of the onboarding/initiation process.

---

### Solution

# 1. Pick delivery method early on

By reminding customers to pick delivery method when they opened the app as a guest user, it will greatly reduce the chance of out of stock items at checkout.

![New Flow (mid)](/images/work_pnsdelivery_flownew.png)

&nbsp;

# 2. Communicate why it is important to pick delivery method at this moment

Communicating the message is harder than we thought. In quick user testing, some users missed message if it is only on the select delivery method page. After including it in every page the majority of the testers understood.

![Prompt Copy (mid)](/images/work_pnsdelivery_why.png)

&nbsp;

# 3. Reduce friction

Communicating the message is harder than we thought. In quick user testing, some users missed message if it is only on the select delivery method page. After including it in every page the majority of the testers understood.

![Reduce Friction (mid)](/images/work_pnsdelivery_reducefriction.png)

---

### Results

# A significant decrease in the number of customer complaints.

Since the implementation of the change, we have seen a significant decrease in the number of customer complaints on out-of-stock & location related issues. As for the exact metrics after the change, we did not get it from the data team. However, product management and business did not request any follow up action, which means the change is a success.
