---
title: 'Components Demo'
subtitle: 'PARKnSHOP'
date: '2023-09-20'
coverImage: '/images/thumb_pnsdesignsystem.jpg'
showOnHomepage: false
---

# Work Post Components Demo

This document demonstrates all the available components used in work posts within the `app/work/` directory.

## 1. WorkSummary Component

The WorkSummary component displays project metadata in a responsive 3-column grid layout (desktop) and single column (mobile). It uses the `!worksummary[...]` syntax.

### Basic Example

!worksummary[Product:Web application|Timeline:6 months|Role:Lead Designer]

### Extended Example with Multiple Fields

!worksummary[Product:B2C, Web & Mobile App|Timeline:1 Year|Role:UI/UX Designer|Team:Jo <PERSON> <PERSON><PERSON> Lead, Phoebe - Senior UX Designer, Me - UI/UX Designer, Jason - UI/UX Designer|Skills:UX Research, UI Design, Prototyping, Design Systems|Company:PARKnSHOP (1500+ employees)]

&nbsp;

---

## 2. WorkLink Component

The WorkLink component creates attractive linked cards to external websites or any custom URL using the `!worklink[title|subtitle|image|url]` syntax.

### Example External Links in Grid

Consecutive links automatically form a 2-column grid:

!worklink[Figma|Collaborative design tool|/images/thumb_elevate.jpg|https://www.figma.com]
!worklink[GitHub|Code repository hosting|/images/thumb_platforms.jpg|https://github.com]
!worklink[Dribbble|Design inspiration|/images/thumb_citysuper.jpg|https://dribbble.com]
!worklink[Behance|Creative portfolios|/images/thumb_harrisonhub.jpg|https://www.behance.net]

### Single Link Example

This single link displays with full width:

!worklink[About Page|Learn more about me|/images/thumb_uxaudit.jpg|/about]

### Another Grid Section

More consecutive links in a separate grid:

!worklink[Adobe XD|Design and prototyping|/images/thumb_onenetwork.jpg|https://www.adobe.com/products/xd.html]
!worklink[Sketch|Digital design toolkit|/images/thumb_parknshop.jpg|https://www.sketch.com]

**Key Features:**

- External links automatically open in new tabs
- Internal links stay in the same tab
- External link icon appears for external URLs
- Same visual style as WorkSection thumbnails
- Automatic image URL processing
- **Automatically groups consecutive links into a 2-column grid layout**
- **Single links display with full width**
- **Responsive design: grid becomes single column on mobile**

&nbsp;

---

## 3. WorkPoints Component

The WorkPoints component displays 2-column text points with titles and subtitles using the `!workpoints[...]` syntax. It's perfect for highlighting key features, benefits, or project outcomes.

### Basic Example

!workpoints[User Research:Conducted interviews with 15 users to understand pain points|Information Architecture:Restructured navigation based on card sorting results|Visual Design:Created a cohesive design system with 40+ components|Usability Testing:Validated designs with 8 participants across 3 iterations]

### Extended Example with More Points

!workpoints[Problem Identification:Users struggled to find products due to poor navigation structure|Research & Analysis:Analyzed user behavior data and conducted stakeholder interviews|Design Strategy:Developed a mobile-first approach with progressive enhancement|Prototyping:Built interactive prototypes to test navigation concepts|User Testing:Conducted moderated usability sessions with target users|Implementation:Collaborated with developers to ensure pixel-perfect execution|Results Measurement:Tracked key metrics showing 40% improvement in task completion|Stakeholder Alignment:Presented findings to C-level executives and secured buy-in]

**Key Features:**

- **2-column layout** on desktop, single column on mobile
- **Responsive design** that adapts to different screen sizes
- **Supports stacking** multiple WorkPoints components
- **Clean typography** with proper spacing and hierarchy
- **Dark mode support** with appropriate color schemes

&nbsp;

---

## 3.5. WorkPoints Variants

The WorkPoints component now has several variants to support different layouts and widths:

### WorkPointsThreeCol - 3-Column Layout (1200px max-width)

The 3-column variant displays points in 3 columns on desktop (1 on mobile, 2 on tablet, 3 on desktop) with 1200px max-width:

!workpointsthreecol[User Research:Conducted interviews with 15 users to understand pain points & behaviors|Information Architecture:Restructured navigation based on card sorting & tree testing results|Visual Design:Created a cohesive design system with 40+ reusable components|Usability Testing:Validated designs with 8 participants across 3 testing iterations|Accessibility:Ensured WCAG 2.1 AA compliance throughout the design process|Performance:Optimized loading times resulting in 40% faster page loads]

### WorkPointsMid - 2-Column with Mid-Width (1200px)

The mid-width variant uses the same 2-column layout but with 1200px max-width and full viewport behavior:

!workpointsmid[Problem Identification:Users struggled to find products due to poor navigation structure & overwhelming product categories|Research & Analysis:Analyzed user behavior data, conducted stakeholder interviews & competitive analysis|Design Strategy:Developed a mobile-first approach with progressive enhancement & accessibility focus|Prototyping:Built interactive prototypes to test navigation concepts & user flows|User Testing:Conducted moderated usability sessions with target users across different demographics|Implementation:Collaborated with developers to ensure pixel-perfect execution & performance optimization]

### WorkPointsThreeColMid - 3-Column with Mid-Width (1200px)

The 3-column mid-width variant combines 3-column layout with 1200px max-width behavior:

!workpointsthreecolmid[Discovery Phase:Stakeholder interviews & user research to understand business goals|Analysis Phase:Data analysis & competitive research to identify opportunities|Strategy Phase:Information architecture & user journey mapping for optimal flow|Design Phase:Wireframing, prototyping & visual design with design system integration|Testing Phase:Usability testing & stakeholder feedback to validate design decisions|Launch Phase:Developer handoff, QA testing & post-launch performance monitoring]

**Variant Features:**

- **`!workpoints[...]`**: Original 2-column layout (stays within normal content width)
- **`!workpointsthreecol[...]`**: 3-column layout with 1200px max-width (1→2→3 columns responsive)
- **`!workpointsmid[...]`**: 2-column with 1200px max-width, full viewport behavior
- **`!workpointsthreecolmid[...]`**: 3-column with 1200px max-width, full viewport behavior
- **All variants**: Maintain consistent styling, spacing, and dark mode support
- **Responsive**: All variants adapt gracefully across different screen sizes
- **1200px max-width**: Three new variants break out of normal content constraints like WorkCarousel
- **HTML parsing**: All variants now properly parse HTML content in subtitles

### Syntax Examples

```markdown
<!-- Original 2-column (normal content width) -->

!workpoints[Title 1:Subtitle 1|Title 2:Subtitle 2]

<!-- 3-column with 1200px max-width -->

!workpointsthreecol[Title 1:Subtitle 1|Title 2:Subtitle 2|Title 3:Subtitle 3]

<!-- 2-column with 1200px max-width -->

!workpointsmid[Title 1:Subtitle 1|Title 2:Subtitle 2]

<!-- 3-column with 1200px max-width (same as workpointsthreecol) -->

!workpointsthreecolmid[Title 1:Subtitle 1|Title 2:Subtitle 2|Title 3:Subtitle 3]
```

&nbsp;

---

## 4. WorkStats Component

The WorkStats component displays statistics in a responsive grid layout with large numbers/percentages and descriptive text below. It automatically adapts the layout based on the number of stats provided.

### 2 Stats Example

!workstats[38%:people reported difficulty in discovering APIs they require|30%:people reported lack of knowledge of existing resources]

### 3 Stats Example (as shown in screenshot)

!workstats[38%:people reported difficulty in discovering APIs they require|30%:people reported lack of knowledge of existing resources|29%:people reported lack of available time to spend in discovery of those resources]

### 4+ Stats Example (stacking behavior)

!workstats[85%:user satisfaction increase|42%:reduction in support tickets|3.2x:faster task completion|67%:improvement in user retention|91%:positive feedback score|28%:decrease in bounce rate]

**Key Features:**

- **Responsive grid layout** that adapts based on the number of stats
- **Large, prominent numbers** with smaller descriptive text below
- **Left-aligned text** for better readability
- **Supports 2, 3, or more stats** with automatic column management
- **Dark mode support** with appropriate color schemes

&nbsp;

---

## 5. Image Components

### Standard Images

Regular images with alt text:

![Standard image example](/images/work_citysuper_cover.jpg)

### Wide Images

Images that span the full width using the `(wide)` modifier:

![Wide image example (wide)](/images/work_parknshop_cover.jpg)

### Images with Captions

Images with descriptive captions using the `:caption=` syntax:

![Process diagram :caption=Our design process from research to implementation](/images/work_parknshop_process.png)

![User journey mapping :caption=Detailed user journey analysis and pain point identification](/images/work_citysuper_journeymap.jpg)

&nbsp;

---

## 6. WorkHighlight Component

The WorkHighlight component creates a highlighted pullout section with an icon and content. It's perfect for emphasizing important information, key insights, or special notes within your work posts.

### Basic Example

!workhighlight[Through workshops and stakeholder alignment, we mapped the challenges, identified inefficiencies, and co-created solutions for faster activation.]

### Example with HTML Content

!workhighlight[This component supports <strong>HTML content</strong> including <em>emphasis</em> and <a href="#" target="_blank">links</a> for rich formatting within the highlight section.]

&nbsp;

---

## 7. Side-by-Side Images

The SideBySideImages component displays two images side by side using the `!side-by-side[...]` syntax. It now supports different sizes:

### Default Size (1200px max-width, centered)

!side-by-side[Before redesign|/images/work_pns_ds_before_1.jpg|After redesign|/images/work_pns_ds_before_2.jpg]

### Mid Size (1200px max-width, full viewport behavior)

!side-by-side[Mobile view|/images/work_citysuper_cover.jpg|Desktop view|/images/work_parknshop_cover.jpg] (mid)

### Wide Size (1632px max-width, full viewport behavior)

!side-by-side[Original design|/images/work_citysuper_final_1.jpg|Final design|/images/work_citysuper_final_2.jpg] (wide)

&nbsp;

---

## 8. Image + Text Side-by-Side

The ImageTextSideBySide component displays an image alongside text content in a flexible layout. It supports left/right positioning, optional titles, and size variants.

### Left Image Position (with title)

!imagetext[/images/work_citysuper_cover.jpg|Create Versatile Extractor|Create Versatile Extractor|User starts creating their custom extractor by uploading samples for our system to learn. We enhance the samples upload on our backend and nudge users to upload more for better results|left]

### Right Image Position (without title)

!imagetext[/images/work_parknshop_cover.jpg|Make Labelling Easy|We empower users to set up the extractor by letting them label the samples. When they instruct the system how to extract data just as they would input manually, we foster trust in our system.|right]

### Mid Size with Left Image

!imagetext[/images/work_citysuper_final_1.jpg|Enhanced User Experience|Enhanced User Experience|The new design system provides a cohesive experience across all touchpoints, improving user satisfaction and reducing cognitive load through consistent patterns and interactions.|left] (mid)

### Wide Size with Right Image

!imagetext[/images/work_citysuper_final_2.jpg|Scalable Design System|Scalable Design System|Built with modularity in mind, the design system scales across different product lines and use cases while maintaining brand consistency and design quality.|right] (wide)

### Video Support

!imagetext[/images/work_citysuper_cover.jpg|Interactive Prototypes|Interactive Prototypes|High-fidelity prototypes help stakeholders understand the user experience and provide valuable feedback during the design process.|left]

&nbsp;

---

## 9. Combined Examples

### Project Overview with Multiple Components

!worksummary[Product:Premium grocery e-commerce|Timeline:3 Months|Role:Solo Product Designer|Company:city'super (1500+ employees)|Skills:UX Research, UI Design, Stakeholder Management]

This project involved redesigning the online shopping experience for city'super, a premium supermarket chain. The main challenge was bringing the online experience up to par with their exceptional physical shopping experience.

![Project cover image (wide)](/images/work_citysuper_cover.jpg)

### Design Process Documentation

The design process involved multiple stages of research, ideation, and testing:

![Research findings :caption=Customer survey analysis and thematic grouping](/images/work_citysuper_affinitymap.jpg)

### Navigation Design Iterations

We explored different navigation patterns and their trade-offs:

![Navigation patterns :caption=Four main navigation patterns with pros and cons analysis](/images/work_citysuper_navigation.jpg)

### Before and After Comparisons

!side-by-side[Original navigation|/images/work_citysuper_navigationcompare.jpg|Improved navigation|/images/work_citysuper_navigationcompare.jpg]

### Final Design Showcase

![Final homepage design (wide)](/images/work_citysuper_final_1.jpg)

![Final product pages (wide)](/images/work_citysuper_final_2.jpg)

### Related Work

Check out other related projects using WorkLink components:

!worklink[PARKnSHOP Case Study|Omni-channel shopping experiences for 1M+ users|/images/thumb_parknshop.jpg|/work/parknshop]

!worklink[UX Audit Case Study|Comprehensive UX evaluation and recommendations|/images/thumb_uxaudit.jpg|/work/ux-audit]

&nbsp;

---

## 9. Typography and Content Structure

### Headings and Text Formatting

# Main Project Title

## Section Heading

### Subsection Heading

**Bold text** for emphasis and _italic text_ for subtle emphasis.

### Lists and Content Organization

Key insights from the project:

- Online/Offline cross-selling is very common
- Shoppers expect discounts in physical stores to be present online
- Additional online and VIP exclusive offers are highly valued
- Product selection should match between online and physical stores

### Numbered Lists

Our design process:

1. **Research Phase**: Customer surveys, analytics analysis, stakeholder interviews
2. **Define Phase**: Persona creation, journey mapping, pain point identification
3. **Ideate Phase**: Navigation pattern exploration, competitive analysis
4. **Design Phase**: Wireframing, prototyping, visual design
5. **Test Phase**: Stakeholder feedback, usability testing
6. **Implement Phase**: Developer handoff, design QA

### Quotes and Callouts

> "The new navigation system significantly improved product discoverability and reduced customer support tickets related to finding products." - eCommerce Manager

&nbsp;

---

## 10. Advanced Layout Examples

### Multi-Column Content with Images

The redesign addressed several key areas:

#### Navigation Improvements

Changed from hamburger menu to category-based top navigation for better discoverability.

![Navigation comparison](/images/work_citysuper_navigationcompare.jpg)

#### Carousel Enhancements

Improved banner navigation with always-visible arrows and VIP exclusive promotions.

![VIP promotions :caption=Exclusive VIP member promotions in hero carousel](/images/work_citysuper_vip.jpg)

#### Brand Showcase

Added dedicated brand section to highlight premium product selection.

![Brand section design](/images/work_citysuper_brandsection.jpg)

### Complex Project Metadata

!worksummary[Product:Enterprise HR SaaS Platform|Timeline:18 months|Role:Senior Product Designer|Team:Sarah Chen - Product Manager, Mike Johnson - Engineering Lead, Lisa Wang - UX Researcher, David Kim - Frontend Developer, Anna Rodriguez - Backend Developer|Skills:User Research, Information Architecture, Interaction Design, Prototyping, Design Systems, Stakeholder Management|Methodology:Design Thinking, Agile Development, Lean UX|Tools:Figma, Miro, Hotjar, Google Analytics|Company:Orgvue (500+ employees)|Budget:$2.5M|Users:50,000+ enterprise users]

&nbsp;

---

## Component Usage Guidelines

### WorkSummary Best Practices

- Use concise labels (Product, Timeline, Role, Team, Skills, etc.)
- Keep values brief but descriptive
- Include 3-8 items for optimal layout
- Use pipe `|` to separate label:value pairs

### WorkPoints Best Practices

- Use descriptive titles that clearly identify the point or feature
- Keep subtitles informative but concise
- Include 2-8 points for optimal layout (displays in 2 columns on desktop)
- Use pipe `|` to separate title:subtitle pairs
- Perfect for highlighting key features, benefits, or project outcomes
- Can be stacked multiple times for different categories of information

### WorkPoints Variants Best Practices

- **`!workpoints[...]` (Original)**: Use for standard 2-column layouts within normal content width
- **`!workpointsthreecol[...]`**: Use when you have 6+ points and want better visual distribution (3 columns on desktop, 1200px max-width)
- **`!workpointsmid[...]`**: Use when you need 2-column layout but want to break out of content constraints (1200px max-width)
- **`!workpointsthreecolmid[...]`**: Use for 6+ points with mid-width behavior (3 columns, 1200px max-width)
- **Choose based on content**: More points work better with 3-column variants
- **Consider context**: Mid-width variants work well for emphasizing important sections
- **Responsive behavior**: All variants gracefully adapt from 1→2→3 columns based on screen size
- **HTML support**: All variants now support HTML formatting in subtitles (bold, italic, links, etc.)

### WorkStats Best Practices

- Use clear, prominent values (percentages, numbers, metrics)
- Keep descriptions concise but informative
- Include 2-6 stats for optimal layout (2-3 columns on desktop)
- Use pipe `|` to separate value:description pairs
- Perfect for highlighting key metrics, research findings, or project impact
- Values should be easily scannable (38%, 2.5x, 150+, etc.)

### WorkHighlight Best Practices

- Use for emphasizing important insights, key findings, or critical information
- Keep content concise but impactful - aim for 1-3 sentences
- Perfect for highlighting stakeholder quotes, design principles, or key takeaways
- Supports HTML formatting for emphasis (bold, italic, links)
- Use sparingly - 1-2 highlights per page for maximum impact
- Great for breaking up long sections of text with important callouts

### WorkLink Best Practices

- Use descriptive titles that clearly identify the destination
- Include helpful subtitles that explain what users will find
- Choose relevant images that represent the linked content
- Use full URLs for external links (https://example.com)
- External links automatically open in new tabs for better UX
- Internal links can use relative paths (/about) or absolute paths

### Image Best Practices

- Use `(wide)` modifier for full-width hero images
- Add captions with `:caption=` for context
- Optimize images for web (WebP format preferred)
- Use descriptive alt text for accessibility

### Side-by-Side Image Best Practices

- Use for before/after comparisons
- Keep image dimensions similar for best results
- Use descriptive labels for each image
- Ensure both images are relevant to the comparison
- **Size options**: Default (centered, 1200px max), Mid (full viewport, 1200px max), Wide (full viewport, 1632px max)
- Use `(mid)` for images that should break out of content width but stay within 1200px
- Use `(wide)` for images that need maximum width and visual impact

### ImageTextSideBySide Best Practices

- Use for feature explanations, process descriptions, or showcasing design details
- **Syntax options**: 4-part (no title) or 5-part (with title) for flexible content structure
- **Position options**: `left` or `right` to control image placement relative to text
- **Size options**: Default (centered, 1200px max), Mid (full viewport, 1200px max), Wide (full viewport, 1632px max)
- Keep descriptions concise but informative - aim for 1-3 sentences
- Use titles to highlight key features or benefits
- Choose images that directly relate to and support the text content
- **Video support**: Automatically handles video files with proper autoplay, loop, and muted attributes
- **Responsive design**: Stacks vertically on mobile, side-by-side on desktop
- Perfect for explaining design decisions, showcasing features, or walking through processes
- Use `left` positioning when the image provides context for what follows in the text
- Use `right` positioning when the text explains or describes what's shown in the image

&nbsp;

---

_This demo showcases all the major components available for work posts. Each component is designed to be responsive and accessible, providing a rich content experience for portfolio case studies._
