---
title: 'Elevate Platform'
subtitle: 'AI-powered Web & competitor monitoring platform'
date: '2025-09-24'
coverImage: '/images/thumb_elevate.jpg'
showOnHomepage: true
---

![Image 2 (wide)](/images/work_elevate_cover.jpg)

!worksummary[Platform: Web|Responsibilities: User Research \
Competitive Analysis \
Rapid Prototyping \
User Testing \
Design Systems |Team: Jamie - Technical Director \
**Me - Solo Product Designer** \
Pete - Software Engineer \
Tom - Software Engineer
]

### Context

# Dusted creates a tool to help managed services clients track & monitor both their own and competitors' marketing websites.

Dusted is a branding and digital agency based in London, primarily serving B2B clients in sectors such as law, professional services, HR executive search, and finance.

**I led product design as the solo product designer on the project.**

---

### Problem

# Solving for emerging managed services client and internal business needs.

!workpoints[Managed services clients: Management has identified several pain points that our current toolset doesn't address.|Our agency: To improve our competitiveness in digital services and introduce tier in pricing.

We will explore and apply new technologies where appropriate to address these and other client needs.]

---

### Target audience

# Two types of marketers kept showing up in our conversations

!workpoints[Time-poor marketers in small teams:Need to see the bottom line, identify anomalies quickly, and get actionable insights without deep dives unless necessary.

However currently they will have to go through different complicated tools to get what they need.|Marketers unfamiliar with using web data: Need guidance and a simplified view of what matters and why.

So that they know if it is worth it to invest in the right places.]

---

### User research

# To understand the needs and painpoints addressable

## 1. Quick second-hand research

Understand client behavior by speaking with project managers who have regular catch-ups with our managed services clients, helping us gain deeper insight into their pain points.

!workpointsthreecol[Purpose: Have a quick understanding of what are the problems to solve|Participants: Managed services project managers|Methods:in-person and remote conversations]

&nbsp;

## 2. 1-on-1 interviews

Without being able to directly access clients at this stage, I have created

!workpointsthreecol[Purpose: To understand the detail's of how marketers work|Participants: Finance Corporate marketer \
in-house marketer|Methods:Remote interviews]

&nbsp;

## 3. Data analysis

AI is used across the research process to summarise and find themes, we also tried asking it to role-play a particular persona as an experiment.

!workpoints[Method: Thematic analysis|Results: A set of pain points to assess the problem space and what we can best solve]

![Competitor list:caption= Summarised and consolidated themes and painpoints](/images/work_elevate_thematic_analysis.jpg)

---

### Competitor research

# What solutions are currently available in the market

In user research we have accoumnilated a list of software marketers use, in combination with further research we have created a competitor list.

!workpoints[Method: Competitor list \
Offering levels map \
Feature research|Result: We decided on our positioning and the level of offering we are going for.]

!carousel[/images/work_elevate_competitor_list.jpg|Competitor list|Competitor list|/images/work_elevate_competitor_offerings.png|Offering Levels Map|Offering Levels Map|/images/work_elevate_competitor_offerings_features.jpg|Feature Analysis|Feature Analysis]

---

### Product direction

# Prioritised 2 directions to explore and prototype

After looking at the problems, the market and what we can solve in a few rounds of internal discussion, we decided the core features (by importance):

1. Easy to understand website metrics + customisable email reporting
2. Competitor website monitoring and comparison

---

### Explorations & collaborative iterations

# We rapidly prototypes (both Figma and coded) to assess both tech feasibility and market-fit

!workpointsthreecol[Methods: Figma prototypes \
coded prototypes (by engineers) \
User testing|Testing participants: Internal team \
Selected managed services clients|Results: Quickly validate designs]

We chose a semi-detailed design using realistic data to help marketers better understand and judge if the interface meets their needs.

At the same time, we also used coded prototypes (sometimes scrappy), to evaluate usefulness especially for AI features such as AI summaries.

Below are some feature explorations:

&nbsp;

## Easy to understand website metrics - Grading system

We explored several ways to display metrics, including simplified benchmarks like "excellent," "good," and "average." However, we found that scores alone are too abstract and tend to raise more questions than they answer.

!side-by-side[UI before redesig|/images/work_elevate_prototype_page_based.jpg|UI after redesign|/images/work_elevate_prototype_simplified_grading.jpg] (mid)

&nbsp;

## Competitor monitoring - Displaying history

We explored storing competitor page history and tracking changes over time, but found that users weren't very interested in this feature, and it would require significantly more technical resources.

!side-by-side[UI before redesign|work_elevate_prototype_history.jpg|UI after redesign|work_elevate_prototype_time_history.jpg] (mid)

&nbsp;

## Competitor comparison - Small set of competitors

We also tried different ways to compare competitors, including layouts that focus on just a few. However, clients prefer seeing more competitors and tend to group them into different categories based on how they compete.

!side-by-side[UI before redesign|work_elevate_prototype_leaderrank.jpg|UI after redesign|/images/work_elevate_prototype_barchart.jpg] (mid)

---

### AI Features exploration

# Designed concepts while engineers and the technical director created coded prototypes for evaluation.

&nbsp;

## AI Recommendations

At that stage, AI recommendations weren’t very useful, often producing generic content. The only area where we saw potential—based on our input data and prompt design—was in generating summaries of competitor website updates.

!side-by-side[UI before redesign|work_elevate_ai_summaries_1.jpg|UI after redesign|/images/work_elevate_ai_summaries_2.jpg] (mid)

&nbsp;

## AI Chatbots

We considered implementing a chatbot, but it faced the same issues as AI recommendations—users often didn’t know what to ask, and the responses lacked useful depth.

!side-by-side[UI before redesign|work_elevate_ai_chatbots_1.jpg|UI after redesign|/images/work_elevate_ai_chatbots_3.jpg] (mid)

---

### MVP

# A focused tool with room to grow

Through multiple rounds of iteration and testing we have consolidate a commonly agreen design for the first version.

&nbsp;

## Feature 1: Track website performance

We decided to present clear, essential metrics like total traffic, conversion rates, channel performance, and basic competitor comparisons for context.

![Image 1 (mid)](/images/work_elevate_mvp_1.jpg)

&nbsp;

## Feature 2: Track competitor website performance

We previously explored a grading system but found that a percentage-based approach gives better detail, helping clients more accurately compare themselves to competitors.

For deeper insights, users can explore individual categories like traffic, conversion, and SEO.

![Image 1 (mid)](/images/work_elevate_mvp_competitors.jpg)

&nbsp;

## Feature 3: Email reporting

WWith weekly email reporting, clients can customize emails for different stakeholder groups—choosing the timing, frequency, and content. For example, C-suite executives can receive a simplified version showing only top-level metrics.

![Image 2 (mid)](/images/work_elevate_mvp_2.jpg)

---

### Future roadmap

# Roll out AI features

This product is developed alongside client projects, resulting in a longer development cycle. AI features require extensive testing/iterating before production to minimize hallucinations and incorrect outputs. These features include:

&nbsp;

# Competitor updates summary

Introducing competitor website tracking to the platform will allow users to quickly identify and understand strategic or product-related changes by competitors. Initial testing revealed that fine-tuning this feature is complex, as the AI must accurately determine the relevance and significance of updates—from minor text changes to major content or design overhauls—to effectively inform marketers.

![Image 2 (mid)](/images/work_elevate_ai_updates_summary.jpg)

&nbsp;

# AI recommendations

As AI models improve and the platform and its data sources mature, we plan to run a new round of testing to see if the recommendations could become valuable for clients.

---

### Outcome

# Still in the development process

Since this project is being built alongside client work, the MVP is still in development and may continue to evolve.

With AI advancing rapidly, there's ongoing discussion about how data tools might benefit from it in the long term.

Ideas like AI agents that can make website changes are also emerging, especially as some companies push toward an "agentic web"—a shift that could change websites fundamentally.

Overall, this has been a valuable learning experience in building a 0–1 product with the Dusted team and exploring various possibilities.
