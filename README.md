<img src="/public/cover.jpg" alt="Cover image representing <PERSON><PERSON>, a personal website template" width="100%" />

# <PERSON>

A modern, high-performance portfolio website built with Next.js 15, React 19, TypeScript, and Tailwind CSS v4. This project showcases best practices in web development with a focus on performance, accessibility, and maintainability.

Live demo: [https://nim-fawn.vercel.app](https://nim-fawn.vercel.app)

## ✨ Features

### Core Features

- **Modern Stack**: Next.js 15, React 19, TypeScript, Tailwind CSS v4
- **Static Export**: Fully static site generation for optimal performance
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Dark Mode**: System preference detection with manual toggle
- **Blog Support**: MDX-powered blog with syntax highlighting
- **Work Portfolio**: Showcase projects with rich media support
- **SEO Optimized**: Comprehensive meta tags and structured data

### Performance & Optimization

- **Image Optimization**: Responsive images with WebP/AVIF support
- **Progressive Loading**: LQIP (Low Quality Image Placeholder) generation
- **Bundle Optimization**: Code splitting and tree shaking
- **Core Web Vitals**: Optimized for excellent performance scores
- **Incremental Builds**: Smart caching for faster development

### Developer Experience

- **TypeScript**: Full type safety with strict configuration
- **ESLint & Prettier**: Comprehensive code quality rules
- **Error Boundaries**: Graceful error handling
- **Custom Hooks**: Reusable logic for common patterns
- **Component Library**: Well-organized, reusable components

## 🚀 Getting Started

### Prerequisites

- Node.js 18.18.0 or higher
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/kevincfchu/portfolio-2025-nextjs.git
   cd portfolio-2025-nextjs
   ```

2. **Install dependencies**

   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env.local
   ```

   Configure the following variables:

   ```env
   NEXT_PUBLIC_SITE_URL=http://localhost:3000
   NEXT_PUBLIC_ENABLE_AMPLITUDE=false
   NEXT_PUBLIC_HOTJAR_ID=your_hotjar_id
   NEXT_PUBLIC_HOTJAR_SV=6
   ```

4. **Start the development server**

   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Development Scripts

```bash
# Development
npm run dev              # Start development server
npm run type-check       # Run TypeScript type checking
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues automatically

# Building
npm run build            # Full production build
npm run build:next       # Build Next.js only
npm run preview          # Build and start production server

# Image Processing
npm run img              # Analyze and optimize images with context-aware responsive variants
npm run clean:images     # Clean optimized images

# Utilities
npm run generate:work    # Generate work data from markdown
npm run clean            # Clean build artifacts
npm run analyze          # Analyze bundle size
```

## 📁 Project Structure

```
portfolio-2025-nextjs/
├── app/                          # Next.js App Router
│   ├── components/              # Reusable React components
│   │   ├── ui/                 # Base UI components
│   │   ├── ErrorBoundary.tsx   # Error handling
│   │   ├── SEO.tsx             # SEO utilities
│   │   └── ...                 # Feature components
│   ├── constants/              # Application constants
│   ├── hooks/                  # Custom React hooks
│   ├── lib/                    # Utility libraries
│   ├── types/                  # TypeScript type definitions
│   ├── utils/                  # Helper functions
│   ├── globals.css             # Global styles
│   ├── layout.tsx              # Root layout
│   └── page.tsx                # Homepage
├── _posts/                      # Blog posts (MDX)
├── _work/                       # Work portfolio (MDX)
├── images/                      # Source images
├── public/                      # Static assets
│   └── images/                 # Optimized images (generated)
├── scripts/                     # Build scripts
│   ├── optimize-images.js      # Image optimization
│   └── generate-work-data.js   # Work data generation
├── next.config.mjs             # Next.js configuration
├── tailwind.config.js          # Tailwind CSS configuration
├── tsconfig.json               # TypeScript configuration
└── eslint.config.mjs           # ESLint configuration
```

## 🎨 Customization

### Site Configuration

Edit `app/constants/index.ts` to customize site-wide settings:

```typescript
export const SITE_CONFIG = {
  name: 'Your Name',
  title: 'Your Title',
  description: 'Your Description',
  url: 'https://yoursite.com',
  author: {
    name: 'Your Name',
    email: '<EMAIL>',
    // ... other social links
  },
  // ... other settings
}
```

## 🔧 Architecture & Patterns

### Component Organization

- **UI Components**: Base components in `app/components/ui/`
- **Feature Components**: Domain-specific components
- **Error Boundaries**: Comprehensive error handling
- **Custom Hooks**: Reusable logic patterns

### Type Safety

- **Strict TypeScript**: Full type coverage with strict mode
- **Type Definitions**: Centralized types in `app/types/`
- **Runtime Validation**: Type-safe environment variables

### Performance Optimizations

- **Bundle Splitting**: Automatic code splitting
- **Image Optimization**: Multi-format responsive images
- **Caching**: Intelligent build caching
- **Core Web Vitals**: Optimized for performance metrics

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines and feel free to open issues or submit pull requests.

## Environment Variables

This project uses environment variables to control various features and configurations. You'll need to set these up for both development and production environments.

### Required Environment Variables

| Variable                       | Description                        | Development             | Production               |
| ------------------------------ | ---------------------------------- | ----------------------- | ------------------------ |
| `NEXT_PUBLIC_ENABLE_AMPLITUDE` | Enable/disable Amplitude analytics | `false`                 | `true`                   |
| `NEXT_PUBLIC_ENABLE_HOTJAR`    | Enable/disable Hotjar tracking     | `false`                 | `true`                   |
| `NEXT_PUBLIC_SITE_URL`         | Your site's URL                    | `http://localhost:3000` | `https://yourdomain.com` |
| `NEXT_PUBLIC_IMAGE_DOMAIN`     | Domain for image optimization      | `localhost:3000`        | `yourdomain.com`         |

### Local Development Setup

1. Copy the example environment files:

   ```bash
   cp .env.example .env
   ```

2. Update `.env` with your development values:
   ```env
   NEXT_PUBLIC_HOSTNAME=http://localhost:3000
   NEXT_PUBLIC_ENABLE_AMPLITUDE=false
   NEXT_PUBLIC_ENABLE_HOTJAR=false
   ```

### Production Deployment

#### For Cloudflare Pages:

1. Go to your Cloudflare Pages dashboard
2. Select your project
3. Navigate to **Settings** → **Environment variables**
4. Add these **Production** environment variables:
   - `NEXT_PUBLIC_ENABLE_AMPLITUDE` = `true`
   - `NEXT_PUBLIC_ENABLE_HOTJAR` = `true`
   - `NEXT_PUBLIC_SITE_URL` = `https://yourdomain.com`
   - `NEXT_PUBLIC_IMAGE_DOMAIN` = `yourdomain.com`

#### For Netlify:

Environment variables are configured in `netlify.toml`:

```toml
[build.environment]
  NEXT_PUBLIC_ENABLE_AMPLITUDE = "true"
  NEXT_PUBLIC_ENABLE_HOTJAR = "true"
  NEXT_PUBLIC_SITE_URL = "https://yourdomain.com"
  NEXT_PUBLIC_IMAGE_DOMAIN = "yourdomain.com"
```

#### For Vercel:

1. Go to your Vercel dashboard
2. Select your project
3. Navigate to **Settings** → **Environment Variables**
4. Add the production environment variables listed above

### Analytics Setup

#### Amplitude Analytics

1. Sign up for [Amplitude](https://amplitude.com)
2. Get your API key from the project settings
3. Update the Amplitude initialization in `app/layout.tsx` with your API key
4. Set `NEXT_PUBLIC_ENABLE_AMPLITUDE=true` in production

#### Hotjar Tracking

1. Sign up for [Hotjar](https://hotjar.com)
2. Get your Hotjar ID and version from your account
3. Update the Hotjar component in `app/components/Hotjar.tsx` with your ID
4. Set `NEXT_PUBLIC_ENABLE_HOTJAR=true` in production

## Deployment

You can deploy your site to any hosting platform that supports Next.js. For the easiest deployment experience, consider using Vercel:

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fibelick%2Fnim&env=NEXT_PUBLIC_SITE_URL&project-name=nim&repository-name=nim&redirect-url=https%3A%2F%2Ftwitter.com%2Fibelick&demo-title=Nim&demo-description=Nim%20is%20a%20free%20and%20open-source%20minimal%20personal%20website%20template%20built%20with%20Next.js%2015%2C%20React%2019%2C%20and%20Motion-Primitives.&demo-url=https%3A%2F%2Fnim.vercel.app&demo-image=https%3A%2F%2Fraw.githubusercontent.com%2Fibelick%2Fnim%2Frefs%2Fheads%2Fmain%2F.github%2Fassets%2Freadme.png&teamSlug=ibelick)

## About

Nim is designed to make personal branding effortless and beautiful. If you enjoy it, consider sharing it and exploring [Motion-Primitives Pro](https://pro.motion-primitives.com/).

## Blog System

This project includes a blog system that uses Markdown files stored in the `_posts` directory. Each Markdown file represents a blog post and includes front matter for metadata:

```markdown
---
title: 'Your Post Title'
date: 'YYYY-MM-DD'
author: 'Author Name'
excerpt: 'A short excerpt of the post content.'
coverImage: 'https://example.com/image.jpg'
---

# Post Content

Your markdown content goes here...
```

### Adding a New Blog Post

To add a new blog post:

1. Create a new `.md` file in the `_posts` directory
2. Add the required front matter at the top (title, date, excerpt)
3. Write your content in Markdown format
4. The post will automatically appear in the blog list

### Technology

The blog system uses:

- `gray-matter` for parsing front matter
- `remark` and `remark-html` for converting Markdown to HTML
- Next.js dynamic routes for creating individual blog post pages

### File Structure

- `_posts/` - Contains all blog post markdown files
- `app/blog/page.tsx` - The blog index page that lists all posts
- `app/blog/[slug]/page.tsx` - Dynamic page for individual blog posts
- `app/blog/layout.tsx` - Layout component for all blog pages
- `lib/api.ts` - API functions for loading and parsing blog posts

## Images

This project uses Next.js Image component for optimized images in a static export. Images should be placed in the `/images` directory at the project root (not in `/public`).

### Adding Images

1. Place your original, high-quality images in the appropriate subdirectory under `/images` (e.g., `/images/work/` for work-related images)
2. Reference images in markdown content using regular markdown syntax:

   ```md
   ![Alt text](/work/image-name.jpg)
   ```

   The path will be automatically prefixed with `/images` during rendering.

3. For imported images in component code:

   ```tsx
   import myImage from '@/images/work/image-name.jpg'

   // Then use it with the Image component
   ;<Image src={myImage} alt="Description" />
   ```

### Context-Aware Image Optimization

This project features an advanced context-aware responsive image system that:

- **Analyzes actual usage patterns** - Detects how images are displayed on your site
- **Generates context-specific variants** - Creates optimized sizes for thumbnails, content images, wide images, etc.
- **Hi-DPI optimization** - Provides 1x, 2x, and 3x variants for retina displays with variable quality
- **Modern formats** - Outputs AVIF, WebP, and original formats with automatic fallbacks
- **Performance insights** - Identifies oversized images and optimization opportunities

Run `npm run img` to analyze your images and generate optimized responsive variants. The system automatically detects contexts like:

- **Thumbnails** (16:9 aspect ratio, grid layouts)
- **Regular content images** (max 800px width)
- **Mid-width images** (max 1200px width)
- **Wide images** (max 1632px width)
- **Side-by-side images** (comparison layouts)
- **Carousel images** (gallery displays)

All images are automatically optimized and context-appropriate sizes are served based on actual usage patterns and device capabilities.
