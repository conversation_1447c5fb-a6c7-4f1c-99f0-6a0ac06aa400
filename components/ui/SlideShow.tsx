import React, { useState, useEffect, useCallback, useRef } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SlideImage {
  src: string
  alt: string
  caption?: string
}

interface SlideshowProps {
  images: SlideImage[]
  className?: string
}

export default function Slideshow({ images, className }: SlideshowProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const slideListRef = useRef<HTMLUListElement>(null)

  const totalSlides = images.length

  const goToNext = useCallback(() => {
    if (isTransitioning) return

    setIsTransitioning(true)
    setCurrentIndex((prevIndex) => (prevIndex + 1) % totalSlides)

    setTimeout(() => {
      setIsTransitioning(false)
    }, 300)
  }, [totalSlides, isTransitioning])

  const goToPrev = useCallback(() => {
    if (isTransitioning) return

    setIsTransitioning(true)
    setCurrentIndex((prevIndex) => (prevIndex - 1 + totalSlides) % totalSlides)

    setTimeout(() => {
      setIsTransitioning(false)
    }, 300)
  }, [totalSlides, isTransitioning])

  // Auto-advance slides every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      goToNext()
    }, 5000)

    return () => clearInterval(interval)
  }, [goToNext])

  // Calculate transform style for slides
  const slideTransform = `translateX(calc(-${currentIndex * 100}%))`

  return (
    <div className={cn('relative w-full overflow-hidden', className)}>
      <div className="absolute top-0 left-0 z-10 flex h-8 items-center px-4 text-sm text-gray-800 dark:text-gray-200">
        <span>{currentIndex + 1}</span>
        <span className="mx-1">/</span>
        <span>{totalSlides}</span>
      </div>

      <div className="relative overflow-hidden">
        <ul
          ref={slideListRef}
          className="flex transition-transform duration-300 ease-in-out"
          style={{ transform: slideTransform }}
        >
          {images.map((image, index) => (
            <li
              key={index}
              className="h-full w-full flex-shrink-0"
              aria-hidden={currentIndex !== index}
            >
              <div className="relative">
                <img
                  src={image.src}
                  alt={image.alt}
                  className="aspect-video w-full object-contain"
                />
                {image.caption && (
                  <div className="absolute right-0 bottom-0 left-0 bg-white/80 p-4 text-center text-gray-800 dark:bg-black/80 dark:text-gray-200">
                    <span>{image.caption}</span>
                  </div>
                )}
              </div>
            </li>
          ))}
        </ul>
      </div>

      <div className="absolute top-0 right-0 z-10 flex gap-1 p-2">
        <button
          onClick={goToPrev}
          aria-label="Previous slide"
          className="flex h-10 w-10 items-center justify-center rounded-full text-gray-800 transition-colors hover:bg-black/10 dark:text-gray-200 dark:hover:bg-white/10"
        >
          <ChevronLeft size={24} />
        </button>
        <button
          onClick={goToNext}
          aria-label="Next slide"
          className="flex h-10 w-10 items-center justify-center rounded-full text-gray-800 transition-colors hover:bg-black/10 dark:text-gray-200 dark:hover:bg-white/10"
        >
          <ChevronRight size={24} />
        </button>
      </div>
    </div>
  )
}
