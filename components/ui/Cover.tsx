import Image from 'next/image'

interface CoverProps {
  src: string
  alt: string
  caption?: string
}

export function Cover({ src, alt, caption }: CoverProps) {
  return (
    <figure className="m-0">
      <div className="relative aspect-video w-full overflow-hidden rounded-lg">
        <Image
          src={src}
          alt={alt}
          width={800}
          height={600}
          priority
          className="w-full h-auto object-contain transition-all duration-500 ease-out"
        />
      </div>
      {caption && (
        <figcaption className="mt-2 text-center text-sm text-gray-500 dark:text-zinc-400">
          {caption}
        </figcaption>
      )}
    </figure>
  )
}
