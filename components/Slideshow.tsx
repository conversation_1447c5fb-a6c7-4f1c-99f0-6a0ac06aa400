'use client';

import Image from 'next/image';
import { useState, useEffect, useCallback, useRef } from 'react';

const ChevronLeftIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 20 20"
    fill="currentColor"
    width="20"
    height="20"
  >
    <path
      fillRule="evenodd"
      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
      clipRule="evenodd"
    />
  </svg>
);

const ChevronRightIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 20 20"
    fill="currentColor"
    width="20"
    height="20"
  >
    <path
      fillRule="evenodd"
      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
      clipRule="evenodd"
    />
  </svg>
);

export interface SlideImage {
  src: string;
  alt: string;
  caption?: string;
}

interface SlideshowProps {
  images: SlideImage[];
}

const Slideshow: React.FC<SlideshowProps> = ({ images }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [effectiveSlidesPerView, setEffectiveSlidesPerView] = useState(2.3); // Default for desktop
  const trackRef = useRef<HTMLDivElement>(null);

  const totalImages = images.length;

  const updateSlidesPerView = useCallback(() => {
    const desktopBreakpoint = 768; // Tailwind's 'md' breakpoint
    if (window.innerWidth < desktopBreakpoint) {
      setEffectiveSlidesPerView(1.2); // Show 1 image and a hint of the second
    } else {
      setEffectiveSlidesPerView(2.3); // Show 2 images and a hint of the third
    }
  }, []);

  useEffect(() => {
    updateSlidesPerView(); // Initial call
    window.addEventListener('resize', updateSlidesPerView);
    return () => window.removeEventListener('resize', updateSlidesPerView);
  }, [updateSlidesPerView]);

  if (!images || totalImages === 0) {
    return (
      <div className="py-8 text-center text-gray-500 dark:text-gray-400">No images to display.</div>
    );
  }

  const goToPrevious = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? totalImages - 1 : prevIndex - 1));
  };
  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex === totalImages - 1 ? 0 : prevIndex + 1));
  };

  const slideItemWidthPercentage = 100 / effectiveSlidesPerView;
  const translateXPercentage = currentIndex * slideItemWidthPercentage;

  return (
    <div className="mx-auto w-full max-w-5xl">
      <div className="mb-2 flex items-center justify-between px-2">
        <div className="text-sm text-zinc-700 dark:text-zinc-200">
          {totalImages > 0 ? `${currentIndex + 1} / ${totalImages}` : '0 / 0'}
        </div>
        {totalImages > 1 && (
          <div className="flex gap-2">
            <button
              onClick={goToPrevious}
              className="h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white hover:bg-gray-50 disabled:opacity-50 dark:border-zinc-700 dark:bg-zinc-900 dark:hover:bg-zinc-800"
              aria-label="Previous slide"
              disabled={totalImages <= 1}
            >
              <ChevronLeftIcon />
            </button>
            <button
              onClick={goToNext}
              className="h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white hover:bg-gray-50 disabled:opacity-50 dark:border-zinc-700 dark:bg-zinc-900 dark:hover:bg-zinc-800"
              aria-label="Next slide"
              disabled={totalImages <= 1}
            >
              <ChevronRightIcon />
            </button>
          </div>
        )}
      </div>

      <div className="overflow-hidden">
        <div
          ref={trackRef}
          className="flex transition-transform duration-300 ease-out"
          style={{
            transform: `translateX(-${translateXPercentage}%)`,
          }}
        >
          {images.map((image, index) => (
            <div
              key={image.src + '-' + index}
              className="px-2"
              style={{ minWidth: `${slideItemWidthPercentage}%` }}
            >
              <figure className="flex aspect-video flex-col items-center justify-center overflow-hidden rounded-lg bg-zinc-100 dark:bg-zinc-800">
                <div className="relative h-64 w-full md:h-80">
                  <Image
                    src={image.src}
                    alt={image.alt}
                    fill
                    sizes={`(max-width: 767px) 83vw, 43vw`}
                    style={{ objectFit: 'cover' }}
                    className="rounded-lg object-cover"
                  />
                </div>
                {image.caption && (
                  <figcaption className="px-2 pb-2 text-center text-sm text-zinc-700 dark:text-zinc-200">
                    {image.caption}
                  </figcaption>
                )}
              </figure>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Slideshow;
