@import 'tailwindcss';
@plugin '@tailwindcss/typography';
@custom-variant dark (&:is(.dark *));

/* Configure Public Sans as the default font */
@theme {
  --font-family-sans: var(--font-public-sans), ui-sans-serif, system-ui,
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

/* Set the root font size to 20px */
:root {
  font-size: 20px;
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }

  /* Set base font weight to 300 */
  body {
    font-weight: 300;
  }
}

/* Custom typography spacing adjustments */
@layer typography {
  .prose {
    /* Reduce the large gap between h1 and h2 */
    & h1 + h2 {
      margin-top: 0.75em;
    }

    /* Adjust other heading spacing if needed */
    & h2 {
      margin-top: 1.5em;
      margin-bottom: 0.75em;
    }

    /* Ensure consistent paragraph margins */
    & p {
      margin-left: 0;
      margin-right: 0;
    }

    /* Override prose figure margins to remove default spacing */
    & figure {
      margin-top: 0;
      margin-bottom: 0;
    }

    /* Ensure no horizontal padding on mobile */
    padding-left: 0;
    padding-right: 0;
  }
}

/* Progressive image loading animations */
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Smooth transitions for progressive loading */
.progressive-image {
  transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.progressive-image.loading {
  opacity: 0;
}

.progressive-image.loaded {
  opacity: 1;
}

.homepage-wide-breakout {
  position: relative !important;
  left: 50% !important;
  right: 50% !important;
  width: 100vw !important;
  max-width: min(100vw, 1632px) !important;
  margin-left: -50vw !important;
  margin-right: -50vw !important;
}

@media (min-width: 768px) {
  .homepage-wide-breakout {
    width: calc(100vw - 48px) !important;
    margin-left: calc(-50vw + 24px) !important;
    margin-right: calc(-50vw + 24px) !important;
  }
}

@media (min-width: 1632px) {
  .homepage-wide-breakout {
    width: 1632px !important;
    left: 50% !important;
    margin-left: -816px !important;
    margin-right: 0 !important;
  }
}
