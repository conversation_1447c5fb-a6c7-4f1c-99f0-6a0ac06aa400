// Core application types

export interface Post {
  slug: string
  title: string
  date: string
  excerpt: string
  content: string
  visibility: boolean
  coverImage?: string
  tags?: string[]
}

export interface WorkItem {
  slug: string
  title: string
  subtitle?: string
  description: string
  image: string
  url?: string
  tags?: string[]
  year?: string
  client?: string
  role?: string
  duration?: string
  team?: string[]
  technologies?: string[]
  featured?: boolean
  order?: number
  visibility?: boolean
  password?: string
}

export interface WorkContent {
  workItem: WorkItem
  content: string
}

// Component prop types
export interface ImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  quality?: number
  sizes?: string
  fill?: boolean
  loading?: 'lazy' | 'eager'
}

export interface ProgressiveImageProps extends ImageProps {
  placeholder?: string
  blurDataURL?: string
  onLoad?: () => void
  onError?: () => void
}

export interface CarouselProps {
  images: Array<{
    src: string
    alt: string
    caption?: string
  }>
  className?: string
  showCaptions?: boolean
  autoplay?: boolean
  interval?: number
}

export interface WorkStatsProps {
  stats: Array<{
    value: string
    label: string
  }>
  className?: string
}

export interface WorkPointsProps {
  points: Array<{
    title: string
    subtitle?: string
  }>
  columns?: 2 | 3
  variant?: 'default' | 'mid' | 'wide'
  className?: string
}

export interface WorkSummaryProps {
  points: string[]
  className?: string
}

export interface SideBySideImagesProps {
  images: [
    {
      src: string
      alt: string
      caption?: string
    },
    {
      src: string
      alt: string
      caption?: string
    },
  ]
  variant?: 'default' | 'mid' | 'wide'
  className?: string
}

// Theme types
export type Theme = 'light' | 'dark' | 'system'

export interface ThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
  resolvedTheme?: Theme
}

// Navigation types
export interface NavItem {
  label: string
  href: string
  external?: boolean
  icon?: React.ReactNode
}

// Form types
export interface ContactFormData {
  name: string
  email: string
  message: string
  subject?: string
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// Animation types
export interface AnimationConfig {
  duration?: number
  delay?: number
  ease?: string | number[]
  repeat?: number
  repeatType?: 'loop' | 'reverse' | 'mirror'
}

// SEO types
export interface SEOProps {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'profile'
  publishedTime?: string
  modifiedTime?: string
  author?: string
  section?: string
  tags?: string[]
}

// Error types
export interface AppError {
  message: string
  code?: string
  statusCode?: number
  stack?: string
}

// Environment types
export interface EnvironmentConfig {
  NODE_ENV: 'development' | 'production' | 'test'
  NEXT_PUBLIC_SITE_URL: string
  NEXT_PUBLIC_HOSTNAME?: string
  NEXT_PUBLIC_ENABLE_AMPLITUDE?: string
  NEXT_PUBLIC_HOTJAR_ID?: string
  NEXT_PUBLIC_HOTJAR_SV?: string
}

// Component children types
export type Children = React.ReactNode
export type ChildrenProps = {
  children: Children
}

// Event handler types
export type ClickHandler = (event: React.MouseEvent<HTMLElement>) => void
export type ChangeHandler = (event: React.ChangeEvent<HTMLInputElement>) => void
export type SubmitHandler = (event: React.FormEvent<HTMLFormElement>) => void
