'use client'

/**
 * Simple client-side crypto utilities for password protection
 * Uses XOR encryption for lightweight protection suitable for static sites
 * Note: This is not cryptographically secure and should only be used for basic content protection
 */

/**
 * Simple XOR encryption/decryption function
 * Same function works for both encryption and decryption
 */
function xorCrypt(content: string, password: string): string {
  if (!content || !password) return ''
  
  let result = ''
  for (let i = 0; i < content.length; i++) {
    // XOR each character with the corresponding character in the password
    const passChar = password.charCodeAt(i % password.length)
    const contentChar = content.charCodeAt(i)
    result += String.fromCharCode(contentChar ^ passChar)
  }
  return result
}

/**
 * Encrypt content using XOR and encode to base64
 */
export function encryptContent(content: string, password: string): string {
  if (!content || !password) return ''
  
  try {
    const encrypted = xorCrypt(content, password)
    // Convert to base64 for safe storage
    return btoa(encrypted)
  } catch (error) {
    console.error('Encryption failed:', error)
    return ''
  }
}

/**
 * Decrypt content from base64 using XOR
 */
export function decryptContent(encryptedContent: string, password: string): string {
  if (!encryptedContent || !password) return ''
  
  try {
    // Decode from base64
    const encrypted = atob(encryptedContent)
    // Decrypt using XOR (same function as encryption)
    return xorCrypt(encrypted, password)
  } catch (error) {
    console.error('Decryption failed:', error)
    throw new Error('Failed to decrypt content. Please check your password.')
  }
}

/**
 * Verify password by attempting to decrypt and checking for valid content
 * This is a simple heuristic - in a real implementation you'd want proper password verification
 */
export function verifyPassword(encryptedContent: string, password: string): boolean {
  try {
    const decrypted = decryptContent(encryptedContent, password)
    // Simple heuristic: decrypted content should contain common markdown patterns
    return decrypted.length > 0 && (
      decrypted.includes('#') || 
      decrypted.includes('![') || 
      decrypted.includes('](') ||
      decrypted.includes('\n\n') ||
      decrypted.includes('**')
    )
  } catch {
    return false
  }
}

/**
 * Session management for authenticated state
 */
export const sessionManager = {
  /**
   * Set authentication state for a specific post
   */
  setAuthenticated(slug: string, password: string): void {
    try {
      localStorage.setItem(`auth_${slug}`, 'true')
      localStorage.setItem(`auth_${slug}_timestamp`, Date.now().toString())
      // Store a hash of the password for verification (simple approach)
      localStorage.setItem(`auth_${slug}_hash`, btoa(password))
    } catch (error) {
      console.warn('Failed to save authentication state:', error)
    }
  },

  /**
   * Check if user is authenticated for a specific post
   */
  isAuthenticated(slug: string): boolean {
    try {
      const token = localStorage.getItem(`auth_${slug}`)
      const timestamp = localStorage.getItem(`auth_${slug}_timestamp`)
      
      if (!token || !timestamp) {
        return false
      }
      
      // Check if session is still valid (24 hours)
      const sessionAge = Date.now() - parseInt(timestamp)
      const maxAge = 24 * 60 * 60 * 1000 // 24 hours
      
      if (sessionAge > maxAge) {
        this.clearAuthentication(slug)
        return false
      }
      
      return true
    } catch (error) {
      console.warn('Failed to check authentication state:', error)
      return false
    }
  },

  /**
   * Clear authentication state for a specific post
   */
  clearAuthentication(slug: string): void {
    try {
      localStorage.removeItem(`auth_${slug}`)
      localStorage.removeItem(`auth_${slug}_timestamp`)
      localStorage.removeItem(`auth_${slug}_hash`)
    } catch (error) {
      console.warn('Failed to clear authentication state:', error)
    }
  },

  /**
   * Clear all authentication states
   */
  clearAllAuthentication(): void {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('auth_')) {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.warn('Failed to clear all authentication states:', error)
    }
  },
}
