/**
 * Utility functions for handling images in a static Next.js site
 */

/**
 * Check if we're in development mode
 */
const isDevelopment = process.env.NODE_ENV === 'development'

/**
 * Check if a file is a video based on its extension
 * @param src The source file path
 * @returns Boolean indicating if the file is a video
 */
export function isVideoFile(src: string): boolean {
  return Boolean(src.match(/\.(mp4|webm|ogg|mov)($|\?)/i))
}

/**
 * Check if a file is a GIF image
 * @param src The source file path
 * @returns Boolean indicating if the file is a GIF
 */
export function isGifFile(src: string): boolean {
  return Boolean(src.match(/\.gif($|\?)/i))
}

/**
 * Normalize a media source path to ensure it has the correct prefix
 * @param src The source media path
 * @returns The normalized path with correct prefix
 */
export function normalizeMediaPath(src: string): string {
  // If src starts with /work/ change it to /images/work/
  if (src.startsWith('/work/')) {
    return `/images${src}`
  }

  // If src starts with / but not with /images/, add /images prefix
  if (src.startsWith('/') && !src.startsWith('/images/')) {
    return `/images${src}`
  }

  // If src doesn't start with /, prepend /images/
  if (!src.startsWith('/')) {
    return `/images/${src}`
  }

  return src
}

/**
 * Generates a low-quality image placeholder (LQIP) as base64
 * @param width The width of the placeholder
 * @param height The height of the placeholder
 * @param color The base color for the placeholder
 * @returns A base64 data URL for the placeholder
 */
export function generateLQIP(
  width: number = 10,
  height: number = 6,
  color: string = '#f3f4f6',
): string {
  // Create a simple SVG placeholder with the specified dimensions and color
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${color}"/>
    </svg>
  `

  // Convert to base64
  const base64 = Buffer.from(svg).toString('base64')
  return `data:image/svg+xml;base64,${base64}`
}

/**
 * Generates a blurred placeholder for progressive loading
 * @param aspectRatio The aspect ratio of the image (width/height)
 * @param isDark Whether to use dark theme colors
 * @returns A base64 data URL for the blurred placeholder
 */
export function generateBlurPlaceholder(
  aspectRatio: number = 16 / 9,
  isDark: boolean = false,
): string {
  const width = 40
  const height = Math.round(width / aspectRatio)
  const baseColor = isDark ? '#1f2937' : '#f3f4f6'
  const gradientColor = isDark ? '#111827' : '#e5e7eb'

  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${baseColor};stop-opacity:1" />
          <stop offset="50%" style="stop-color:${gradientColor};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${baseColor};stop-opacity:1" />
        </linearGradient>
        <filter id="blur">
          <feGaussianBlur stdDeviation="2"/>
        </filter>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)" filter="url(#blur)"/>
    </svg>
  `

  const base64 = Buffer.from(svg).toString('base64')
  return `data:image/svg+xml;base64,${base64}`
}

/**
 * Generates an optimized image URL for static exports
 * @param src The source image path
 * @param width The requested width
 * @param quality The image quality (1-100)
 * @returns The URL with optimization parameters
 */
export function getOptimizedImageUrl(
  src: string,
  width: number = 1200,
  quality: number = 90,
): string {
  // For static exports, we need to ensure the path is correct
  let processedSrc = normalizeMediaPath(src)

  // If it's a video file, just return the normalized path
  if (isVideoFile(processedSrc)) {
    return processedSrc
  }

  // Use responsive variants in both dev and production
  // Prefer AVIF > WebP > Original format
  const originalExt = processedSrc.match(/\.(jpe?g|png|gif|webp|avif)($|\?)/i)
  const ext = originalExt ? originalExt[1].toLowerCase() : 'jpg'

  // Try AVIF first, then WebP, then original
  const formats = ['avif', 'webp', ext]
  const bestFormat = formats.find(format => format === 'avif') ||
                     formats.find(format => format === 'webp') ||
                     ext

  // Generate responsive variant filename
  const baseName = processedSrc.replace(/\.[^.]+$/, '')
  const sizeInfo = `_${width}w`

  return `${baseName}${sizeInfo}.${bestFormat}`
}

/**
 * Generates a srcset string for responsive images
 * @param src The source image path
 * @param quality The image quality (1-100)
 * @returns A srcset string for responsive images
 */
export function getImageSrcSet(src: string, quality: number = 90): string {
  // If it's a video file, return empty string as videos don't use srcset
  if (isVideoFile(src)) {
    return ''
  }

  // For static exports, we need to ensure the path is correct
  let processedSrc = normalizeMediaPath(src)

  // Use responsive variants in both dev and production
  // Prefer AVIF > WebP > Original format
  const originalExt = processedSrc.match(/\.(jpe?g|png|gif|webp|avif)($|\?)/i)
  const ext = originalExt ? originalExt[1].toLowerCase() : 'jpg'

  // Try AVIF first, then WebP, then original
  const formats = ['avif', 'webp', ext]
  const bestFormat = formats.find(format => format === 'avif') ||
                     formats.find(format => format === 'webp') ||
                     ext

  // Generate responsive variant filenames using context-aware sizes
  const baseName = processedSrc.replace(/\.[^.]+$/, '')

  // Detect context and use appropriate sizes
  const context = detectImageContext(processedSrc)
  const contextConfig = IMAGE_CONTEXTS[context]

  const srcSet = contextConfig.dpi
    .map((dpr) => {
      const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
      return `${baseName}${dpiInfo}.${bestFormat} ${dpr}x`
    })
    .join(', ')

  return srcSet
}

// DPI-focused image configurations (Intercom style - matches optimize-images.js and image-loader.js)
export const IMAGE_CONTEXTS = {
  thumbnail: {
    baseWidth: 800,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 800
  },
  regular: {
    baseWidth: 800,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 800
  },
  mid: {
    baseWidth: 1200,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 1200
  },
  wide: {
    baseWidth: 1632,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 1632
  },
  sideBySide: {
    baseWidth: 600,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 600
  },
  carousel: {
    baseWidth: 800,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 800
  }
} as const

type ImageContext = keyof typeof IMAGE_CONTEXTS

/**
 * Detect image context from filename and usage patterns
 */
export function detectImageContext(src: string, width?: number): ImageContext {
  const fileName = src.split('/').pop() || ''

  // Detect by filename patterns
  if (fileName.includes('thumb_')) return 'thumbnail'
  if (fileName.includes('carousel')) return 'carousel'

  // Detect by requested width (fallback)
  if (width) {
    if (width <= 600) return 'sideBySide'
    if (width <= 800) return 'regular'
    if (width <= 1200) return 'mid'
    if (width > 1200) return 'wide'
  }

  return 'regular'
}

/**
 * Generates a DPI-focused srcset (Intercom style)
 * @param src The source image path
 * @param context The image context (thumbnail, regular, mid, wide, etc.)
 * @param preferDensity Whether to prefer density descriptors (1x, 2x) - always true in new approach
 * @returns The srcset string optimized for the specific context
 */
export function generateContextAwareSrcSet(
  src: string,
  context: ImageContext = 'regular',
  preferDensity: boolean = true, // Default to DPI approach
): string {
  // If it's a video file, return empty string as videos don't use srcset
  if (isVideoFile(src)) {
    return ''
  }

  let processedSrc = normalizeMediaPath(src)
  const contextConfig = IMAGE_CONTEXTS[context]

  // Use optimized DPI variants in both dev and production
  const baseName = processedSrc.replace(/\.[^.]+$/, '')
  const originalExt = processedSrc.match(/\.(jpe?g|png|gif|webp|avif)($|\?)/i)

  // Prefer AVIF > WebP > Original
  const formats = ['avif', 'webp', originalExt?.[1]?.toLowerCase() || 'jpg']
  const bestFormat = formats[0] // Use AVIF as primary format

  // Generate DPI-based srcset (Intercom style)
  return contextConfig.dpi
    .map((dpr) => {
      const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
      return `${baseName}${dpiInfo}.${bestFormat} ${dpr}x`
    })
    .join(', ')
}

/**
 * Generate optimal sizes attribute for responsive images
 * @param context The image context
 * @returns The sizes attribute string
 */
export function generateSizesAttribute(context: ImageContext): string {
  switch (context) {
    case 'thumbnail':
      return '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw'
    case 'regular':
      return '(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 800px'
    case 'mid':
      return '(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 1200px'
    case 'wide':
      return '100vw'
    case 'sideBySide':
      return '(max-width: 640px) 100vw, (max-width: 1024px) 45vw, 400px'
    case 'carousel':
      return '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
    default:
      return '(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 800px'
  }
}

/**
 * Returns a base64 encoded blurry placeholder image
 * @returns A base64 blurry placeholder
 */
export function getBlurDataURL(): string {
  return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEJgJYRFi+EAAAAABJRU5ErkJggg=='
}
