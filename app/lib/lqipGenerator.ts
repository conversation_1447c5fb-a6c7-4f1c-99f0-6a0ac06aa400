'use client'

/**
 * LQIP (Low Quality Image Placeholder) Generator
 * Generates base64-encoded low quality placeholders for progressive image loading
 */

interface LQIPOptions {
  width?: number
  height?: number
  quality?: number
  blur?: number
  format?: 'webp' | 'jpeg' | 'png'
}

/**
 * Generates a canvas-based LQIP from an image
 * This runs in the browser and creates actual low-quality versions of images
 */
export async function generateCanvasLQIP(
  imageSrc: string,
  options: LQIPOptions = {},
): Promise<string> {
  const {
    width = 20,
    height = 15,
    quality = 0.1,
    blur = 1,
    format = 'jpeg',
  } = options

  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'

    img.onload = () => {
      try {
        // Create canvas with small dimensions
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')

        if (!ctx) {
          reject(new Error('Could not get canvas context'))
          return
        }

        // Set canvas size
        canvas.width = width
        canvas.height = height

        // Apply blur filter
        ctx.filter = `blur(${blur}px)`

        // Draw the image scaled down
        ctx.drawImage(img, 0, 0, width, height)

        // Convert to base64 with low quality
        const mimeType = `image/${format}`
        const dataURL = canvas.toDataURL(mimeType, quality)

        resolve(dataURL)
      } catch (error) {
        reject(error)
      }
    }

    img.onerror = () => {
      reject(new Error(`Failed to load image: ${imageSrc}`))
    }

    img.src = imageSrc
  })
}

/**
 * Generates a CSS-based gradient placeholder based on dominant colors
 * This is a fallback when canvas LQIP generation fails
 */
export function generateGradientLQIP(
  colors: string[] = ['#f3f4f6', '#e5e7eb'],
  aspectRatio: number = 16 / 9,
  isDark: boolean = false,
): string {
  const defaultLight = ['#f3f4f6', '#e5e7eb', '#d1d5db']
  const defaultDark = ['#1f2937', '#111827', '#0f172a']

  const gradientColors =
    colors.length > 0 ? colors : isDark ? defaultDark : defaultLight

  const width = 40
  const height = Math.round(width / aspectRatio)

  // Create gradient stops
  const stops = gradientColors
    .map((color, index) => {
      const position = (index / (gradientColors.length - 1)) * 100
      return `<stop offset="${position}%" style="stop-color:${color};stop-opacity:1" />`
    })
    .join('')

  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          ${stops}
        </linearGradient>
        <filter id="blur">
          <feGaussianBlur stdDeviation="1.5"/>
        </filter>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)" filter="url(#blur)"/>
    </svg>
  `

  const base64 = btoa(svg)
  return `data:image/svg+xml;base64,${base64}`
}

/**
 * Extracts dominant colors from an image using canvas
 * Used to generate more accurate gradient placeholders
 */
export async function extractDominantColors(
  imageSrc: string,
  colorCount: number = 3,
): Promise<string[]> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')

        if (!ctx) {
          reject(new Error('Could not get canvas context'))
          return
        }

        // Use small canvas for color extraction
        const size = 50
        canvas.width = size
        canvas.height = size

        ctx.drawImage(img, 0, 0, size, size)

        const imageData = ctx.getImageData(0, 0, size, size)
        const data = imageData.data

        // Simple color extraction - sample every nth pixel
        const colors: { [key: string]: number } = {}
        const step = 4 * 10 // Sample every 10th pixel

        for (let i = 0; i < data.length; i += step) {
          const r = data[i]
          const g = data[i + 1]
          const b = data[i + 2]
          const alpha = data[i + 3]

          // Skip transparent pixels
          if (alpha < 128) continue

          // Round colors to reduce variations
          const roundedR = Math.round(r / 32) * 32
          const roundedG = Math.round(g / 32) * 32
          const roundedB = Math.round(b / 32) * 32

          const colorKey = `rgb(${roundedR},${roundedG},${roundedB})`
          colors[colorKey] = (colors[colorKey] || 0) + 1
        }

        // Sort by frequency and take top colors
        const sortedColors = Object.entries(colors)
          .sort(([, a], [, b]) => b - a)
          .slice(0, colorCount)
          .map(([color]) => color)

        resolve(sortedColors.length > 0 ? sortedColors : ['#f3f4f6', '#e5e7eb'])
      } catch (error) {
        reject(error)
      }
    }

    img.onerror = () => {
      reject(new Error(`Failed to load image: ${imageSrc}`))
    }

    img.src = imageSrc
  })
}

/**
 * Smart LQIP generator that tries multiple methods
 * 1. Canvas-based LQIP with actual image data
 * 2. Gradient LQIP with extracted dominant colors
 * 3. Fallback gradient LQIP with default colors
 */
export async function generateSmartLQIP(
  imageSrc: string,
  aspectRatio: number = 16 / 9,
  isDark: boolean = false,
): Promise<string> {
  try {
    // Try to generate canvas-based LQIP
    const canvasLQIP = await generateCanvasLQIP(imageSrc, {
      width: 20,
      height: Math.round(20 / aspectRatio),
      quality: 0.1,
      blur: 1,
    })
    return canvasLQIP
  } catch (error) {
    console.warn(
      'Canvas LQIP generation failed, trying color extraction:',
      error,
    )

    try {
      // Try to extract colors and generate gradient
      const colors = await extractDominantColors(imageSrc, 3)
      return generateGradientLQIP(colors, aspectRatio, isDark)
    } catch (colorError) {
      console.warn(
        'Color extraction failed, using default gradient:',
        colorError,
      )

      // Fallback to default gradient
      return generateGradientLQIP([], aspectRatio, isDark)
    }
  }
}

/**
 * Cache for generated LQIPs to avoid regenerating the same placeholders
 */
const lqipCache = new Map<string, string>()

/**
 * Cached LQIP generator
 */
export async function getCachedLQIP(
  imageSrc: string,
  aspectRatio: number = 16 / 9,
  isDark: boolean = false,
): Promise<string> {
  const cacheKey = `${imageSrc}-${aspectRatio}-${isDark}`

  if (lqipCache.has(cacheKey)) {
    return lqipCache.get(cacheKey)!
  }

  const lqip = await generateSmartLQIP(imageSrc, aspectRatio, isDark)
  lqipCache.set(cacheKey, lqip)

  return lqip
}
