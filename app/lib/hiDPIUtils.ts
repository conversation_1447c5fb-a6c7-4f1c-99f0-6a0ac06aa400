'use client'

import { IMAGE_CONTEXTS } from './imageUtils'

/**
 * Enhanced hi-DPI utilities for optimal image display on high-density screens
 */

/**
 * Detect the device pixel ratio on the client side
 * @returns The current device pixel ratio or 1 as fallback
 */
export function getDevicePixelRatio(): number {
  if (typeof window === 'undefined') return 1
  return window.devicePixelRatio || 1
}

/**
 * Get the optimal DPR for image loading based on device capabilities (Intercom style)
 * @param maxDPR Maximum DPR to consider (default: 2, no 3x support)
 * @returns The optimal DPR value (1x or 2x only)
 */
export function getOptimalDPR(maxDPR: number = 2): number {
  const deviceDPR = getDevicePixelRatio()

  // Simplified DPR detection - only 1x and 2x (Intercom style)
  if (deviceDPR >= 1.5 && maxDPR >= 2) return 2
  return 1
}

/**
 * Generate DPI-focused srcset with smart DPR detection (Intercom style)
 * @param src The source image path
 * @param context The image context
 * @param baseWidth The base width for density calculations (ignored in new approach)
 * @returns DPI-based srcset string with density descriptors (1x, 2x only)
 */
export function generateEnhancedDensitySrcSet(
  src: string,
  context: keyof typeof IMAGE_CONTEXTS,
  baseWidth?: number
): string {
  const contextConfig = IMAGE_CONTEXTS[context]
  const baseName = src.replace(/\.[^.]+$/, '')
  const originalExt = src.match(/\.(jpe?g|png|gif|webp|avif)($|\?)/i)
  const ext = originalExt ? originalExt[1].toLowerCase() : 'jpg'

  // Prefer AVIF, then WebP, then original format
  const formats = ['avif', 'webp', ext]
  const bestFormat = formats.find(format => format === 'avif') ||
                     formats.find(format => format === 'webp') ||
                     ext

  // Use DPI-focused approach (Intercom style)
  return contextConfig.dpi
    .map((dpr) => {
      const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
      return `${baseName}${dpiInfo}.${bestFormat} ${dpr}x`
    })
    .join(', ')
}

/**
 * Generate responsive srcset with width descriptors and hi-DPI support
 * @param src The source image path
 * @param context The image context
 * @param includeHiDPI Whether to include hi-DPI variants (default: true)
 * @returns Enhanced srcset string with width descriptors
 */
export function generateEnhancedResponsiveSrcSet(
  src: string,
  context: keyof typeof IMAGE_CONTEXTS,
  includeHiDPI: boolean = true
): string {
  const contextConfig = IMAGE_CONTEXTS[context]
  const baseName = src.replace(/\.[^.]+$/, '')
  const originalExt = src.match(/\.(jpe?g|png|gif|webp|avif)($|\?)/i)
  const ext = originalExt ? originalExt[1].toLowerCase() : 'jpg'

  // Prefer AVIF, then WebP, then original format
  const formats = ['avif', 'webp', ext]
  const bestFormat = formats.find(format => format === 'avif') ||
                     formats.find(format => format === 'webp') ||
                     ext

  const srcSetEntries: string[] = []

  // Generate DPI-based srcset (Intercom style)
  return contextConfig.dpi
    .map((dpr) => {
      const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
      return `${baseName}${dpiInfo}.${bestFormat} ${dpr}x`
    })
    .join(', ')
}

/**
 * Generate enhanced sizes attribute with hi-DPI considerations
 * @param context The image context
 * @param customSizes Custom sizes string to override defaults
 * @returns Enhanced sizes attribute string
 */
export function generateEnhancedSizesAttribute(
  context: keyof typeof IMAGE_CONTEXTS,
  customSizes?: string
): string {
  if (customSizes) return customSizes

  // Enhanced sizes attributes that consider hi-DPI screens
  switch (context) {
    case 'thumbnail':
      return '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1440px) 33vw, 400px'
    case 'regular':
      return '(max-width: 640px) 100vw, (max-width: 1024px) 90vw, (max-width: 1440px) 800px, 800px'
    case 'mid':
      return '(max-width: 640px) 100vw, (max-width: 1024px) 90vw, (max-width: 1440px) 1200px, 1200px'
    case 'wide':
      return '100vw'
    case 'sideBySide':
      return '(max-width: 640px) 100vw, (max-width: 1024px) 45vw, (max-width: 1440px) 400px, 400px'
    case 'carousel':
      return '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, (max-width: 1440px) 33vw, 400px'
    default:
      return '(max-width: 640px) 100vw, (max-width: 1024px) 90vw, (max-width: 1440px) 800px, 800px'
  }
}

/**
 * Check if the browser supports modern image formats
 * @returns Object indicating support for various formats
 */
export function getImageFormatSupport(): {
  avif: boolean
  webp: boolean
} {
  if (typeof window === 'undefined') {
    return { avif: false, webp: false }
  }

  const canvas = document.createElement('canvas')
  canvas.width = 1
  canvas.height = 1

  return {
    avif: canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0,
    webp: canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
  }
}

/**
 * Get the best image format based on browser support
 * @param originalFormat The original image format
 * @returns The best supported format
 */
export function getBestImageFormat(originalFormat: string): string {
  const support = getImageFormatSupport()

  if (support.avif) return 'avif'
  if (support.webp) return 'webp'
  return originalFormat
}

/**
 * Generate CSS image-set for background images with hi-DPI support
 * @param src The source image path
 * @param context The image context
 * @param baseWidth The base width for calculations
 * @returns CSS image-set string
 */
export function generateCSSImageSet(
  src: string,
  context: keyof typeof IMAGE_CONTEXTS,
  baseWidth?: number
): string {
  const contextConfig = IMAGE_CONTEXTS[context]
  const baseName = src.replace(/\.[^.]+$/, '')
  const originalExt = src.match(/\.(jpe?g|png|gif|webp|avif)($|\?)/i)
  const ext = originalExt ? originalExt[1].toLowerCase() : 'jpg'

  const bestFormat = getBestImageFormat(ext)

  const imageSetEntries = contextConfig.dpi
    .map((dpr) => {
      const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
      return `url("${baseName}${dpiInfo}.${bestFormat}") ${dpr}x`
    })
    .join(', ')

  return `image-set(${imageSetEntries})`
}

/**
 * Preload critical hi-DPI images
 * @param src The source image path
 * @param context The image context
 * @param priority Whether this is a high priority image
 */
export function preloadHiDPIImage(
  src: string,
  context: keyof typeof IMAGE_CONTEXTS,
  priority: boolean = false
): void {
  if (typeof window === 'undefined') return

  const optimalDPR = getOptimalDPR()
  const baseName = src.replace(/\.[^.]+$/, '')
  const bestFormat = getBestImageFormat('jpg')

  // Use DPI-focused approach (Intercom style)
  const dpiInfo = optimalDPR > 1 ? `_${optimalDPR}x` : `_1x`
  const optimizedSrc = `${baseName}${dpiInfo}.${bestFormat}`

  const link = document.createElement('link')
  link.rel = 'preload'
  link.as = 'image'
  link.href = optimizedSrc

  if (priority) {
    link.setAttribute('fetchpriority', 'high')
  }

  document.head.appendChild(link)
}
