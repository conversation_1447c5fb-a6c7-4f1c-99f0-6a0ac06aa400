import { Metadata } from 'next'
import { generateMetadata as generateSEOMetadata } from '@/app/components/SEO'
import { SITE_CONFIG } from '@/app/constants'

// Generate metadata for the about page
export const metadata: Metadata = generateSEOMetadata({
  title: 'About <PERSON> Chu',
  description: 'Learn more about <PERSON>, a UX/Product Designer previously at Dusted, EY & A.S Watson. Based in London, UK.',
  url: `${SITE_CONFIG.url}/about`,
  keywords: ['<PERSON>', 'UX Designer', 'Product Designer', 'London', 'Portfolio', 'About'],
})

export default function AboutLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
