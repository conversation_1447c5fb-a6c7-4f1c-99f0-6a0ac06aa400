'use client'
import { motion } from 'motion/react'
import { StructuredData, generateStructuredData } from '@/app/components/SEO'
import { SITE_CONFIG } from '@/app/constants'
import { Magnetic } from '@/components/ui/magnetic'
import { IMAGE_CONTEXTS } from '@/lib/imageUtils'

const VARIANTS_CONTAINER = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
    },
  },
}

const VARIANTS_SECTION = {
  hidden: { opacity: 0, y: 20, filter: 'blur(8px)' },
  visible: { opacity: 1, y: 0, filter: 'blur(0px)' },
}

const TRANSITION_SECTION = {
  duration: 0.3,
}

// Generate DPI-optimized image with proper srcset (Intercom style)
function generateDPIOptimizedImage(src: string, alt: string, className: string = '', aspectRatio?: string, priority: boolean = false): JSX.Element {
  // Get the base path without extension
  const basePath = src.replace(/\.[^.]+$/, '')

  // Use regular context for about page images
  const contextConfig = IMAGE_CONTEXTS.regular
  const contextDPI = contextConfig.dpi

  // Generate DPI-based srcset (Intercom style)
  const srcset = contextDPI
    .map((dpr) => {
      const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
      return `${basePath}${dpiInfo}.avif ${dpr}x`
    })
    .join(', ')

  // Main src uses 1x variant
  const mainSrc = `${basePath}_1x.avif`

  return (
    <img
      src={mainSrc}
      srcSet={srcset}
      alt={alt}
      className={className}
      style={{
        width: '100%',
        height: '100%',
        aspectRatio: aspectRatio || 'auto'
      }}
      loading={priority ? "eager" : "lazy"}
      decoding="async"
    />
  )
}

function MagneticSocialLink({
  children,
  link,
}: {
  children: React.ReactNode
  link: string
}) {
  return (
    <Magnetic springOptions={{ bounce: 0 }} intensity={0.3}>
      <a
        href={link}
        target="_blank"
        rel="noopener noreferrer"
        className="group relative inline-flex shrink-0 items-center gap-[1px] rounded-full bg-zinc-100 px-2.5 py-1 text-sm text-black no-underline transition-colors duration-200 hover:bg-zinc-950 hover:text-zinc-50 dark:bg-zinc-800 dark:text-zinc-100 dark:hover:bg-zinc-700"
      >
        {children}
        <svg
          width="15"
          height="15"
          viewBox="0 0 15 15"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="h-3 w-3"
        >
          <path
            d="M3.64645 11.3536C3.45118 11.1583 3.45118 10.8417 3.64645 10.6465L10.2929 4L6 4C5.72386 4 5.5 3.77614 5.5 3.5C5.5 3.22386 5.72386 3 6 3L11.5 3C11.6326 3 11.7598 3.05268 11.8536 3.14645C11.9473 3.24022 12 3.36739 12 3.5L12 9.00001C12 9.27615 11.7761 9.50001 11.5 9.50001C11.2239 9.50001 11 9.27615 11 9.00001V4.70711L4.35355 11.3536C4.15829 11.5488 3.84171 11.5488 3.64645 11.3536Z"
            fill="currentColor"
            fillRule="evenodd"
            clipRule="evenodd"
          ></path>
        </svg>
      </a>
    </Magnetic>
  )
}

export default function AboutPage() {
  // Generate structured data for the about page
  const structuredData = generateStructuredData({
    type: 'person',
    title: 'About Kevin Chu',
    description: 'Learn more about Kevin Chu, a UX/Product Designer previously at Dusted, EY & A.S Watson. Based in London, UK.',
    url: `${SITE_CONFIG.url}/about`,
  })

  return (
    <>
      <StructuredData data={structuredData} />
      <motion.main
        className="mx-auto max-w-7xl"
        variants={VARIANTS_CONTAINER}
        initial="hidden"
        animate="visible"
      >
      <motion.section
        variants={VARIANTS_SECTION}
        transition={TRANSITION_SECTION}
        className="mb-32"
      >
        <h1 className="mb-12 text-xl font-medium md:text-3xl">About me</h1>

        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-20">
          {/* Column 1: Text content */}
          <div className="prose dark:prose-invert max-w-none">
            <h3>Experience</h3>
            <p className="mb-4 text-zinc-700 dark:text-zinc-200">
              Previously at Dusted, a brand and digital agency in central
              London, where I designed websites and platforms for startups, law
              firms, and professional services.
            </p>
            <p className="mb-4 text-zinc-700 dark:text-zinc-200">
              Before that, I worked at EY Mtel, designing large-scale
              omni-channel shopping experiences.
            </p>

            <h3>Outside of work</h3>
            <p className="text-zinc-700 dark:text-zinc-200">
              I&apos;m into all things tech — built a homelab with Proxmox and
              Docker, and love experimenting with local LLMs/ML using LM Studio,
              Ollama, and Pinokio.
            </p>
            <p className="text-zinc-700 dark:text-zinc-200">
              I curate and sync ideas with Readwise Reader and Obsidian.
            </p>
            <p className="mb-4 text-zinc-700 dark:text-zinc-200">
              When I&apos;m not geeking out, you&apos;ll probably find me on the
              badminton court or unwinding with a great sci-fi flick.
            </p>
          </div>

          {/* Column 2: Image */}
          <div className="order-first lg:order-last">
            <div className="relative aspect-[1/1] max-h-[500px] max-w-[500px] overflow-hidden rounded-sm">
              {generateDPIOptimizedImage(
                "/images/kevinchu",
                "Kevin Chu portrait photo",
                "object-cover transition-all duration-500 ease-out",
                "1/1",
                true // priority loading for above-the-fold image
              )}
            </div>
          </div>
        </div>
      </motion.section>

      <motion.section
        variants={VARIANTS_SECTION}
        transition={TRANSITION_SECTION}
        className="mb-16"
      >
        <h2 className="mb-8 text-2xl font-medium">Testimonials</h2>
        <div className="grid grid-cols-1 gap-16 md:grid-cols-2">
          {/* First testimonial */}
          <div className="space-y-6">
            <p className="text-zinc-700 dark:text-zinc-200">
              He has been incredible to work with. During his time working at
              the client office, he has received great compliments, citing that
              he has great work ethics and ability to solve UX problems with
              quality and speed. He was also instrumental in helping A.S Watson
              transit from Sketch to Figma.
            </p>
            <div>
              <h3 className="text-xl font-medium">Ling Li</h3>
              <p className="text-zinc-700 dark:text-zinc-200">
                UX Manager at EY Mtel
              </p>
            </div>
            <hr className="border-t border-zinc-200 dark:border-zinc-800" />
          </div>

          {/* Second testimonial */}
          <div className="space-y-6">
            <p className="text-zinc-700 dark:text-zinc-200">
              Kevin has been a great asset to the team. He is a diligent and
              helpful team member who is our &ldquo;go-to person&rdquo; for
              anything related to Figma, After Effect, and beyond. He played a
              huge part in setting up our design system and design operations
              workflow.
            </p>
            <div>
              <h3 className="text-xl font-medium">Jo Lee</h3>
              <p className="text-zinc-700 dark:text-zinc-200">
                UX Lead at A.S Watson
              </p>
            </div>
            <hr className="border-t border-zinc-200 dark:border-zinc-800" />
          </div>

          {/* Third testimonial */}
          <div className="space-y-6">
            <p className="text-zinc-700 dark:text-zinc-200">
              As a young and talented UX/UI designer, Kevin demonstrated a great
              passion towards UX design. He is a problem solver who can work on
              multiple tasks independently. His knowledge in e-commerce and his
              open attitude makes him a joy to collaborate with.
            </p>
            <div>
              <h3 className="text-xl font-medium">Ray Chen</h3>
              <p className="text-zinc-700 dark:text-zinc-200">
                UX Manager at A.S Watson
              </p>
            </div>
            <hr className="border-t border-zinc-200 dark:border-zinc-800" />
          </div>

          {/* Fourth testimonial */}
          <div className="space-y-6">
            <p className="text-zinc-700 dark:text-zinc-200">
              He has been an absolute pleasure to work with, bringing his strong
              work ethic and attention to detail to every project. He frequently
              has been the driving force behind our most creative and
              technological innovations.
            </p>
            <div>
              <h3 className="text-xl font-medium">Kelvin Kwong</h3>
              <p className="text-zinc-700 dark:text-zinc-200">
                Managing Director & Founder at XGD Media
              </p>
            </div>
            <hr className="border-t border-zinc-200 dark:border-zinc-800" />
          </div>
        </div>
        <div className="mt-16">
          <div className="relative aspect-[40/28] overflow-hidden rounded-xl">
            {generateDPIOptimizedImage(
              "/images/kind-words",
              "Kind words from colleagues",
              "object-cover",
              "40/28"
            )}
          </div>
        </div>
      </motion.section>
    </motion.main>
    </>
  )
}
