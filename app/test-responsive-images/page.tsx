import { ResponsiveImage } from '@/components/ResponsiveImage'

export default function TestResponsiveImages() {
  return (
    <div className="mx-auto max-w-4xl px-4 py-8">
      <h1 className="mb-8 text-3xl font-bold">Responsive Images Test</h1>
      
      <div className="space-y-12">
        {/* Thumbnail Context */}
        <section>
          <h2 className="mb-4 text-xl font-semibold">Thumbnail Context</h2>
          <p className="mb-4 text-sm text-gray-600">
            Optimized for grid layouts, 33vw to 100vw responsive behavior
          </p>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <ResponsiveImage
              src="/images/work_elevate_cover.jpg"
              alt="Elevate project thumbnail"
              width={800}
              height={450}
              context="thumbnail"
              className="rounded-lg"
            />
            <ResponsiveImage
              src="/images/work_citysuper_cover.jpg"
              alt="CitySuper project thumbnail"
              width={800}
              height={450}
              context="thumbnail"
              className="rounded-lg"
            />
            <ResponsiveImage
              src="/images/work_parknshop_cover.jpg"
              alt="ParkNShop project thumbnail"
              width={800}
              height={450}
              context="thumbnail"
              className="rounded-lg"
            />
          </div>
        </section>

        {/* Regular Context */}
        <section>
          <h2 className="mb-4 text-xl font-semibold">Regular Context</h2>
          <p className="mb-4 text-sm text-gray-600">
            Standard content images, max 800px width
          </p>
          <ResponsiveImage
            src="/images/work_elevate_cover.jpg"
            alt="Regular context example"
            width={800}
            height={450}
            context="regular"
            className="rounded-lg"
          />
        </section>

        {/* Mid Context */}
        <section>
          <h2 className="mb-4 text-xl font-semibold">Mid Context</h2>
          <p className="mb-4 text-sm text-gray-600">
            Medium-large images, max 1200px width
          </p>
          <ResponsiveImage
            src="/images/work_citysuper_cover.jpg"
            alt="Mid context example"
            width={1200}
            height={675}
            context="mid"
            className="rounded-lg"
          />
        </section>

        {/* Wide Context */}
        <section>
          <h2 className="mb-4 text-xl font-semibold">Wide Context</h2>
          <p className="mb-4 text-sm text-gray-600">
            Full-width images, max 1632px width, 100vw responsive
          </p>
          <ResponsiveImage
            src="/images/work_parknshop_cover.jpg"
            alt="Wide context example"
            width={1632}
            height={918}
            context="wide"
            className="rounded-lg"
          />
        </section>

        {/* Side by Side Context */}
        <section>
          <h2 className="mb-4 text-xl font-semibold">Side by Side Context</h2>
          <p className="mb-4 text-sm text-gray-600">
            Optimized for side-by-side layouts, 45vw responsive behavior
          </p>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <ResponsiveImage
              src="/images/work_elevate_cover.jpg"
              alt="Side by side example 1"
              width={600}
              height={338}
              context="sideBySide"
              className="rounded-lg"
            />
            <ResponsiveImage
              src="/images/work_citysuper_cover.jpg"
              alt="Side by side example 2"
              width={600}
              height={338}
              context="sideBySide"
              className="rounded-lg"
            />
          </div>
        </section>

        {/* Auto-detect Context */}
        <section>
          <h2 className="mb-4 text-xl font-semibold">Auto-detect Context</h2>
          <p className="mb-4 text-sm text-gray-600">
            Let the component automatically detect the best context based on dimensions
          </p>
          <div className="space-y-4">
            <ResponsiveImage
              src="/images/work_parknshop_cover.jpg"
              alt="Auto-detect small"
              width={400}
              height={225}
              autoDetectContext={true}
              className="rounded-lg"
            />
            <ResponsiveImage
              src="/images/work_elevate_cover.jpg"
              alt="Auto-detect medium"
              width={800}
              height={450}
              autoDetectContext={true}
              className="rounded-lg"
            />
            <ResponsiveImage
              src="/images/work_citysuper_cover.jpg"
              alt="Auto-detect large"
              width={1400}
              height={788}
              autoDetectContext={true}
              className="rounded-lg"
            />
          </div>
        </section>
      </div>
    </div>
  )
}
