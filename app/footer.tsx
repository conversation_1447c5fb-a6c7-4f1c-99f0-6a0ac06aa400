'use client'

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faLinkedin,
  faGithub,
  faXTwitter,
} from '@fortawesome/free-brands-svg-icons'

import { TextLoop } from '@/components/ui/text-loop'
import { SITE_CONFIG, NAVIGATION } from '@/constants'
import { cn } from '@/utils'

const iconMap = {
  linkedin: faLinkedin,
  github: faGithub,
  twitter: faXTwitter,
} as const

export function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="w-full border-t border-zinc-100 py-4 dark:border-zinc-800">
      <div className="flex items-center justify-between px-4 md:px-8">
        <TextLoop className="hidden text-xs text-zinc-500 md:block">
          <span>
            © {currentYear} {SITE_CONFIG.name}.
          </span>
          <span>Built with Next.js, deployed on Cloudflare Pages.</span>
        </TextLoop>

        <div className="flex items-center gap-3">
          {NAVIGATION.social.map((social: any) => (
            <a
              key={social.label}
              href={social.href}
              target="_blank"
              rel="noopener noreferrer"
              className={cn(
                'inline-flex h-5 w-5 items-center justify-center',
                'text-zinc-500 hover:text-zinc-950 dark:text-zinc-400 dark:hover:text-zinc-50',
                'transition-colors duration-100',
              )}
              aria-label={social.label}
            >
              <FontAwesomeIcon
                icon={iconMap[social.icon as keyof typeof iconMap]}
                className="h-4 w-4"
              />
            </a>
          ))}
        </div>
      </div>
    </footer>
  )
}
