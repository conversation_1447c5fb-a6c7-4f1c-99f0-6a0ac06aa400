'use client'

import { motion } from 'motion/react'
import { ReactNode, useEffect, useState, useRef } from 'react'

// Animation variants matching homepage and about page
const VARIANTS_CONTAINER = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
    },
  },
}

const VARIANTS_SECTION = {
  hidden: { opacity: 0, y: 20, filter: 'blur(8px)' },
  visible: { opacity: 1, y: 0, filter: 'blur(0px)' },
}

const TRANSITION_SECTION = {
  duration: 0.3,
}

interface WorkPostClientProps {
  children: ReactNode
}

export function WorkPostClient({ children }: WorkPostClientProps) {
  const [shouldAnimate, setShouldAnimate] = useState(true)
  const containerRef = useRef<HTMLDivElement>(null)
  const hasCheckedImages = useRef(false)

  useEffect(() => {
    if (hasCheckedImages.current || shouldAnimate) return

    const container = containerRef.current
    if (!container) {
      setShouldAnimate(true)
      return
    }

    hasCheckedImages.current = true

    const checkForFirstImage = () => {
      // Look for the first img element in the content
      const firstImage = container.querySelector('img')

      if (!firstImage) {
        // No image found, animate immediately
        setShouldAnimate(true)
        return
      }

      // Check if image is already loaded
      if (firstImage.complete && firstImage.naturalHeight !== 0) {
        setShouldAnimate(true)
        return
      }

      // Wait for image to load
      const handleImageLoad = () => {
        setShouldAnimate(true)
        cleanup()
      }

      const handleImageError = () => {
        setShouldAnimate(true)
        cleanup()
      }

      const cleanup = () => {
        firstImage.removeEventListener('load', handleImageLoad)
        firstImage.removeEventListener('error', handleImageError)
      }

      firstImage.addEventListener('load', handleImageLoad)
      firstImage.addEventListener('error', handleImageError)

      // Fallback timeout - don't wait more than 2 seconds
      setTimeout(() => {
        setShouldAnimate(true)
        cleanup()
      }, 2000)
    }

    // Give the DOM time to render, then check for images
    const timeoutId = setTimeout(checkForFirstImage, 200)

    return () => {
      clearTimeout(timeoutId)
    }
  }, [])

  return (
    <div ref={containerRef}>
      <motion.main
        className="space-y-0"
        variants={VARIANTS_CONTAINER}
        initial="hidden"
        animate={shouldAnimate ? "visible" : "hidden"}
      >
        <motion.section
          variants={VARIANTS_SECTION}
          transition={TRANSITION_SECTION}
        >
          {children}
        </motion.section>
      </motion.main>
    </div>
  )
}
