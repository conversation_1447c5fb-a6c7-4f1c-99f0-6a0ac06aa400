import { notFound } from 'next/navigation'
import { <PERSON>ada<PERSON> } from 'next'
import { MDXContent } from '@/app/components/MDXContent'
import fs from 'node:fs'
import path from 'node:path'
import matter from 'gray-matter'
import { remark } from 'remark'
import html from 'remark-html'
import { WorkItem } from '@/app/data/works'
import PasswordProtectionWrapper from '@/app/components/PasswordProtectionWrapper'
import staticWorksData from '@/app/data/works-data.json'
import type { StaticImageData } from 'next/image'
import { generateMetadata as generateSEOMetadata, StructuredData, generateCreativeWorkData } from '@/app/components/SEO'
import { SITE_CONFIG } from '@/app/constants'
import { WorkPostClient } from './WorkPostClient'

// This tells Next.js to statically optimize this page
export const dynamic = 'force-static'

// Helper to determine if we're in development mode
const isDev = process.env.NODE_ENV === 'development'

// Extended WorkItem type including password protection
interface ExtendedWorkItem extends WorkItem {
  passwordLock?: boolean
  password?: string
  encryptedContent?: string
}

/**
 * Get a single work item directly from markdown file
 * Used in development mode for real-time updates
 */
function getWorkFromMarkdown(slug: string): ExtendedWorkItem | null {
  try {
    const fullPath = path.join(process.cwd(), '_work', `${slug}.md`)

    if (!fs.existsSync(fullPath)) {
      return null
    }

    // Read the file content
    const fileContents = fs.readFileSync(fullPath, 'utf8')

    // Parse the frontmatter
    const { data } = matter(fileContents)

    // Create and return the work item
    return {
      slug,
      title: data.title || slug,
      subtitle: data.subtitle || undefined,
      coverImage: data.coverImage || '',
      date: data.date
        ? String(data.date)
        : new Date().toISOString().split('T')[0],
      showOnHomepage:
        data.showOnHomepage !== undefined ? Boolean(data.showOnHomepage) : true,
      passwordLock:
        data.passwordLock !== undefined ? Boolean(data.passwordLock) : false,
      password: data.password || undefined,
      encryptedContent: data.encryptedContent || undefined,
    }
  } catch (error) {
    console.error(`Error reading markdown file for ${slug}:`, error)
    return null
  }
}

/**
 * Get all works either from static data or markdown files
 */
function getAllWorks(): ExtendedWorkItem[] {
  // In development, read directly from markdown files for fresh data
  if (isDev) {
    try {
      const workDirectory = path.join(process.cwd(), '_work')

      if (!fs.existsSync(workDirectory)) {
        console.warn('Work directory not found:', workDirectory)
        return []
      }

      // Get all markdown files
      const fileNames = fs
        .readdirSync(workDirectory)
        .filter((fileName) => fileName.endsWith('.md'))

      // Read and parse all work items directly from files
      return fileNames
        .map((fileName) => {
          const slug = fileName.replace(/\.md$/, '')
          const work = getWorkFromMarkdown(slug)
          return (
            work || {
              slug,
              title: slug,
              coverImage: '',
              date: new Date().toISOString().split('T')[0],
            }
          )
        })
        .sort((a, b) => {
          return new Date(b.date).getTime() - new Date(a.date).getTime()
        })
    } catch (error) {
      console.error('Error loading works from markdown:', error)
      return staticWorksData as ExtendedWorkItem[]
    }
  }

  // In production, use pre-generated static data
  return staticWorksData as ExtendedWorkItem[]
}

/**
 * Get all available work slugs for static generation
 */
export function generateStaticParams() {
  try {
    // In development and production, we need to read directly from the filesystem
    // to ensure we get ALL markdown files, including password-protected ones
    const workDirectory = path.join(process.cwd(), '_work')

    if (!fs.existsSync(workDirectory)) {
      console.warn('Work directory not found:', workDirectory)
      return []
    }

    // Get all markdown files directly from the filesystem
    const fileNames = fs
      .readdirSync(workDirectory)
      .filter((fileName) => fileName.endsWith('.md'))

    // Return slug params for each file
    return fileNames.map((fileName) => ({
      slug: fileName.replace(/\.md$/, ''),
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    // Fallback to work data if filesystem read fails
    const allWorks = getAllWorks()
    return allWorks.map((work) => ({
      slug: work.slug,
    }))
  }
}

/**
 * Get work content by slug
 */
async function getWorkContent(slug: string): Promise<{
  content: string
  workItem: ExtendedWorkItem | null
  encryptedContent?: string
}> {
  try {
    // First try to find the work in data
    let workItem: ExtendedWorkItem | null

    // In development, read fresh data directly from markdown
    if (isDev) {
      workItem = getWorkFromMarkdown(slug)
    } else {
      // In production, use static data
      workItem = getAllWorks().find((work) => work.slug === slug) || null
    }

    if (!workItem) {
      return { content: '', workItem: null, encryptedContent: undefined }
    }

    // Load the markdown content
    const fullPath = path.join(process.cwd(), '_work', `${slug}.md`)

    if (!fs.existsSync(fullPath)) {
      return { content: '', workItem, encryptedContent: undefined }
    }

    // Read the markdown file
    const fileContents = fs.readFileSync(fullPath, 'utf8')
    const { content, data } = matter(fileContents)

    // Process the markdown content
    const processedContent = await remark().use(html).process(content)

    const contentHtml = processedContent.toString()

    // Return the work item with content
    return {
      content: contentHtml,
      workItem: {
        ...workItem,
        content: contentHtml,
        encryptedContent: data.encryptedContent,
      },
      encryptedContent: data.encryptedContent,
    }
  } catch (error) {
    console.error(`Error processing work content for ${slug}:`, error)
    // Fallback to static data without content
    const workItem = isDev
      ? getWorkFromMarkdown(slug)
      : getAllWorks().find((work) => work.slug === slug)
    return {
      content: '',
      workItem: workItem || null,
      encryptedContent: undefined,
    }
  }
}

// Add a function to process image URLs
function getProcessedImageUrl(
  url: string | StaticImageData,
): string | StaticImageData {
  if (typeof url !== 'string') return url

  // If it's already a StaticImageData or starts with /images/, return as is
  if (url.startsWith('/images/')) return url

  // If it starts with /work/, add /images prefix
  if (url.startsWith('/work/')) {
    return `/images${url}`
  }

  // If it starts with / but not with /images/, add /images prefix
  if (url.startsWith('/') && !url.startsWith('/images/')) {
    return `/images${url}`
  }

  // If it doesn't start with /, prepend /images/
  if (!url.startsWith('/')) {
    return `/images/${url}`
  }

  return url
}

// Define the parameters type for static generation
type Params = {
  slug: string
}

// Generate metadata
export async function generateMetadata({
  params,
}: {
  params: Params
}): Promise<Metadata> {
  const resolvedParams = await Promise.resolve(params)
  const slug = resolvedParams.slug

  const { workItem } = await getWorkContent(slug)

  if (!workItem) {
    return {
      title: 'Work not found',
      description: 'The requested work portfolio could not be found',
    }
  }

  const imageUrl = typeof workItem.coverImage === 'string' ? workItem.coverImage : ''

  return generateSEOMetadata({
    title: workItem.title,
    description: workItem.subtitle || workItem.title,
    image: imageUrl,
    url: `${SITE_CONFIG.url}/work/${slug}`,
    type: 'article',
    publishedTime: workItem.date,
    author: SITE_CONFIG.author.name,
  })
}

// The main component
export default async function WorkPost({ params }: { params: Params }) {
  const resolvedParams = await Promise.resolve(params)
  const slug = resolvedParams.slug

  const { workItem, content, encryptedContent } = await getWorkContent(slug)

  if (!workItem) {
    notFound()
  }

  // Generate structured data for the work portfolio
  const imageUrl = typeof workItem.coverImage === 'string' ? workItem.coverImage : ''
  const structuredData = generateCreativeWorkData({
    title: workItem.title,
    description: workItem.subtitle || workItem.title,
    image: imageUrl,
    url: `${SITE_CONFIG.url}/work/${slug}`,
    dateCreated: workItem.date,
    creator: SITE_CONFIG.author.name,
  })

  // Extract the title and subtitle section for rendering
  const titleSection = (
    <>
      <div className="mb-2 text-2xl font-medium text-black md:text-3xl dark:text-white">
        {workItem.title}
      </div>
      {workItem.subtitle && (
        <div className="mt-4 mb-10 text-lg text-zinc-600 md:text-xl dark:text-zinc-400">
          {workItem.subtitle}
        </div>
      )}
    </>
  )

  // Simple approach - just wrap the content in a password protection component if needed
  if (workItem.passwordLock && workItem.password) {
    return (
      <>
        <StructuredData data={structuredData} />
        <WorkPostClient>
          <article className="mx-auto w-full">
            {titleSection}
            <PasswordProtectionWrapper
              password={workItem.password}
              content={content}
              encryptedContent={encryptedContent}
              slug={slug}
            />
          </article>
        </WorkPostClient>
      </>
    )
  }

  // For non-protected content, render normally
  return (
    <>
      <StructuredData data={structuredData} />
      <WorkPostClient>
        <article className="mx-auto w-full">
          {titleSection}
          {content && (
            <MDXContent key={`${slug}-${content.length}`} content={content} />
          )}
        </article>
      </WorkPostClient>
    </>
  )
}
