'use client'
import { ScrollProgress } from '@/components/ui/scroll-progress'

export default function WorkLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <>
      <div className="pointer-events-none fixed top-0 left-0 z-10 h-10 w-full bg-gray-100 to-transparent backdrop-blur-xl [-webkit-mask-image:linear-gradient(to_bottom,black,transparent)] dark:bg-zinc-950" />

      {/* Scroll progress background track - positioned at bottom of header */}
      <div className="fixed top-12 left-0 z-[60] h-1 w-full bg-[#E6F4FE] dark:bg-[#111927]" />

      {/* Scroll progress bar - positioned at bottom of header */}
      <ScrollProgress
        className="fixed top-12 z-[61] h-1 bg-[#0090FF]"
        springOptions={{
          bounce: 0,
        }}
      />

      <main className="mx-auto pb-20 px-3" style={{ maxWidth: '1632px' }}>
        <div
          className="prose prose-gray prose-h4:prose-base dark:prose-invert prose-h1:text-2xl prose-h1:font-medium prose-h2:mt-12 prose-h2:scroll-m-20 prose-h2:text-lg prose-h2:font-medium prose-h3:text-base prose-h3:font-medium prose-h4:font-medium prose-h5:text-base prose-h5:font-medium prose-h6:text-base prose-h6:font-medium prose-strong:font-medium mx-auto max-w-none"
          style={{ maxWidth: '800px' }}
        >
          {children}
        </div>
      </main>

      <style jsx global>{`
        /* Standardize text color for work posts content */
        .prose p,
        .prose li,
        .prose blockquote {
          color: rgb(82 82 91); /* text-zinc-600 */
        }

        /* Ensure bold text within paragraphs, lists, and blockquotes matches the same color */
        .prose p strong,
        .prose li strong,
        .prose blockquote strong {
          color: rgb(82 82 91); /* text-zinc-600 */
        }

        .dark .prose p,
        .dark .prose li,
        .dark .prose blockquote {
          color: rgb(161 161 170); /* dark:text-zinc-400 */
        }

        /* Ensure bold text within paragraphs, lists, and blockquotes matches the same color in dark mode */
        .dark .prose p strong,
        .dark .prose li strong,
        .dark .prose blockquote strong {
          color: rgb(161 161 170); /* dark:text-zinc-400 */
        }

        /* Set mobile font size to 16px for work posts */
        @media (max-width: 767px) {
          :root {
            font-size: 16px;
          }

          /* Make H1 smaller on mobile */
          .prose h1 {
            font-size: 1.25rem; /* 20px with 16px base font size */
            line-height: 1.2;
          }
        }

        /* Add styles for wide images */
        .wide-image {
          max-width: min(100vw, 1632px);
          width: calc(100vw - 32px); /* Account for 16px margins on mobile */
          position: relative;
          left: 50%;
          right: 50%;
          margin-left: calc(-50vw + 16px);
          margin-right: calc(-50vw + 16px);
        }

        /* Desktop and tablet: account for 24px margins */
        @media (min-width: 768px) {
          .wide-image {
            width: calc(
              100vw - 48px
            ); /* Account for 24px margins on desktop/tablet */
            margin-left: calc(-50vw + 24px);
            margin-right: calc(-50vw + 24px);
          }
        }

        /* Ensure wide images don't exceed the page container */
        @media (min-width: 1632px) {
          .wide-image {
            width: 1632px;
            left: 50%;
            margin-left: -816px;
            margin-right: 0;
          }
        }

        /* Add styles for medium images */
        .mid-image {
          max-width: min(100vw, 1200px);
          width: calc(100vw - 32px); /* Account for 16px margins on mobile */
          position: relative;
          left: 50%;
          right: 50%;
          margin-left: calc(-50vw + 16px);
          margin-right: calc(-50vw + 16px);
        }

        /* Desktop and tablet: account for 24px margins */
        @media (min-width: 768px) {
          .mid-image {
            width: calc(
              100vw - 48px
            ); /* Account for 24px margins on desktop/tablet */
            margin-left: calc(-50vw + 24px);
            margin-right: calc(-50vw + 24px);
          }
        }

        /* Ensure medium images don't exceed 1200px */
        @media (min-width: 1200px) {
          .mid-image {
            width: 1200px;
            left: 50%;
            margin-left: -600px;
            margin-right: 0;
          }
        }

        /* Add styles for side-by-side images - default size */
        .side-by-side-image-wrapper.side-by-side-default {
          max-width: 1200px;
          width: 100%;
          margin: 2rem auto;
        }

        /* Add styles for side-by-side images - mid size (use mid-image behavior) */
        .side-by-side-image-wrapper.side-by-side-mid {
          margin: 2rem 0;
        }

        /* Add styles for side-by-side images - wide size (use wide-image behavior) */
        .side-by-side-image-wrapper.side-by-side-wide {
          margin: 2rem 0;
        }
      `}</style>
    </>
  )
}
