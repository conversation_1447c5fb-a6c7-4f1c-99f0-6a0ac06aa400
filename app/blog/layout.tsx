'use client'
import { ScrollProgress } from '@/components/ui/scroll-progress'
import { usePathname } from 'next/navigation'

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()

  // Only show scroll progress on individual blog posts, not on the blog listing page
  const showScrollProgress = pathname !== '/blog'

  return (
    <>
      <div className="pointer-events-none fixed top-0 left-0 z-10 h-12 w-full bg-gray-100 to-transparent backdrop-blur-xl [-webkit-mask-image:linear-gradient(to_bottom,black,transparent)] dark:bg-zinc-950" />

      {showScrollProgress && (
        <>
          {/* Scroll progress background track - positioned just below header */}
          <div className="fixed top-12 left-0 z-[60] h-1 w-full bg-[#E6F4FE] dark:bg-[#111927]" />

          {/* Scroll progress bar - positioned just below header */}
          <ScrollProgress
            className="fixed top-12 z-[61] h-1 bg-[#0090FF]"
            springOptions={{
              bounce: 0,
            }}
          />
        </>
      )}

      <div className="relative mx-auto w-full max-w-screen-lg flex-1 px-3">
        <main className="prose prose-gray prose-h4:prose-base dark:prose-invert prose-h1:text-xl prose-h1:font-medium prose-h2:mt-12 prose-h2:scroll-m-20 prose-h2:text-lg prose-h2:font-medium prose-h3:text-base prose-h3:font-medium prose-h4:font-medium prose-h5:text-base prose-h5:font-medium prose-h6:text-base prose-h6:font-medium pb-20">
          {children}
        </main>
      </div>
    </>
  )
}
