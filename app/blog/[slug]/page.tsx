import { notFound } from 'next/navigation'
import { remark } from 'remark'
import html from 'remark-html'
import { getPostBySlug, getAllPosts } from '../../../lib/posts'
import { MDXContent } from '@/app/components/MDXContent'
import { generateMetadata as generateSEOMetadata, StructuredData, generateStructuredData } from '@/app/components/SEO'
import { SITE_CONFIG } from '@/app/constants'
import { BlogPostClient } from './BlogPostClient'

// This tells Next.js to statically optimize this page
export const dynamic = 'force-static'

// Process markdown content to HTML
async function markdownToHtml(markdown: string) {
  const result = await remark().use(html).process(markdown)
  return result.toString()
}

// Get all available post slugs for static generation
export function generateStaticParams() {
  const posts = getAllPosts()
  return posts.map((post) => ({
    slug: post.slug,
  }))
}

// Generate metadata
export async function generateMetadata({
  params,
}: {
  params: { slug: string }
}) {
  const resolvedParams = await Promise.resolve(params)
  const post = getPostBySlug(resolvedParams.slug)

  if (!post) {
    return {
      title: 'Post not found',
      description: 'The requested blog post could not be found',
    }
  }

  return generateSEOMetadata({
    title: post.title,
    description: post.excerpt,
    image: post.coverImage,
    url: `${SITE_CONFIG.url}/blog/${post.slug}`,
    type: 'article',
    publishedTime: post.date,
    author: SITE_CONFIG.author.name,
    keywords: post.tags || [],
  })
}

// The main component
export default async function Post({ params }: { params: { slug: string } }) {
  const resolvedParams = await Promise.resolve(params)
  const post = getPostBySlug(resolvedParams.slug)

  if (!post) {
    notFound()
  }

  // Convert markdown content to HTML
  const content = post.content ? await markdownToHtml(post.content) : ''

  // Generate structured data for the blog post
  const structuredData = generateStructuredData({
    type: 'article',
    title: post.title,
    description: post.excerpt,
    image: post.coverImage,
    url: `${SITE_CONFIG.url}/blog/${post.slug}`,
    publishedTime: post.date,
    author: SITE_CONFIG.author.name,
  })

  return (
    <>
      <StructuredData data={structuredData} />
      <BlogPostClient>
        <article className="mx-auto max-w-2xl px-0 sm:px-5">
          <div className="mb-2 text-xl font-medium md:text-3xl">{post.title}</div>
          <div className="mb-6 text-sm text-gray-500 dark:text-zinc-400">
            <span>{post.date}</span>
          </div>

          <MDXContent content={content} />
        </article>
      </BlogPostClient>
    </>
  )
}
