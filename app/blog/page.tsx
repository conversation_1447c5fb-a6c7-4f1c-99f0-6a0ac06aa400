import { getAllPosts } from '../../lib/posts'
import { BlogList } from './blog-list'
import { Metadata } from 'next'
import { generateMetadata as generateSEOMetadata } from '@/app/components/SEO'
import { SITE_CONFIG } from '@/app/constants'

// Generate metadata for the blog page
export const metadata: Metadata = generateSEOMetadata({
  title: 'Blog',
  description: 'Read my latest thoughts on UX design, product development, and technology.',
  url: `${SITE_CONFIG.url}/blog`,
  keywords: ['Blog', 'UX Design', 'Product Design', 'Technology', 'Kevin Chu'],
})

export default function Blog() {
  const posts = getAllPosts()

  return <BlogList posts={posts} />
}
