'use client';

import mixpanel from 'mixpanel-browser';
import { ReactNode, useEffect } from 'react';

export function MixpanelProvider({ children }: { children: ReactNode }) {
  useEffect(() => {
    // Initialize Mixpanel with session replay
    mixpanel.init('6c0c711a39715501147b715ed00a272d', {
      debug: process.env.NODE_ENV === 'development',
      track_pageview: true,
      persistence: 'localStorage',
      record_sessions_percent: 100, // Record 100% of all sessions for replay
      loaded: (mixpanel) => {
        // Enable automatic pageview tracking after initialization
        mixpanel.track('Page Loaded', {
          url: window.location.pathname,
          timestamp: new Date().toISOString(),
        });
      },
    });

    // Track page view on route change
    const handleRouteChange = () => {
      mixpanel.track('Page View', {
        url: window.location.pathname,
        timestamp: new Date().toISOString(),
      });
    };

    // Initial page view
    handleRouteChange();

    // Set up route change listener
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  return <>{children}</>;
}
