import type { Metadata, Viewport } from 'next';

import { Public_Sans } from 'next/font/google';
import Script from 'next/script';

import { ErrorBoundary } from './components/ErrorBoundary';
import Hotjar from './components/Hotjar';
import { generateMetadata } from './components/SEO';
import { SITE_CONFIG, ANALYTICS_CONFIG } from './constants';
import { Footer } from './footer';
import { Header } from './header';
import { MixpanelProvider } from './providers/MixpanelProvider';

import './globals.css';

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#ffffff',
}

export const metadata: Metadata = {
  ...generateMetadata({
    title: SITE_CONFIG.title,
    description: SITE_CONFIG.description,
    url: SITE_CONFIG.url,
  }),
  metadataBase: new URL(SITE_CONFIG.url),
}

const publicSans = Public_Sans({
  variable: '--font-public-sans',
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap',
})

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className="dark" suppressHydrationWarning>
      <head>
        {/* Mixpanel Analytics - Only load when enabled */}
        {ANALYTICS_CONFIG.mixpanel.enabled && ANALYTICS_CONFIG.mixpanel.token && (
          <>
            <Script
              id="mixpanel-lib"
              strategy="afterInteractive"
              src="https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js"
            />
            <Script
              id="mixpanel-init"
              strategy="afterInteractive"
              dangerouslySetInnerHTML={{
                __html: `
                  window.mixpanel = window.mixpanel || [];
                  window.mixpanel.init('${ANALYTICS_CONFIG.mixpanel.token}');
                `,
              }}
            />
          </>
        )}
        {/* Amplitude Analytics - Only load when enabled */}
        {ANALYTICS_CONFIG.amplitude.enabled && (
          <>
            <Script
              src="https://cdn.amplitude.com/libs/analytics-browser-2.11.1-min.js.gz"
              strategy="beforeInteractive"
            />
            <Script
              src="https://cdn.amplitude.com/libs/plugin-session-replay-browser-1.8.0-min.js.gz"
              strategy="beforeInteractive"
            />
            <Script id="amplitude-init" strategy="afterInteractive">
              {`
                window.amplitude.add(window.sessionReplay.plugin({
                  sampleRate: ${ANALYTICS_CONFIG.amplitude.sessionReplay.sampleRate}
                }));
                window.amplitude.init('${ANALYTICS_CONFIG.amplitude.apiKey}', {
                  "autocapture": ${JSON.stringify(ANALYTICS_CONFIG.amplitude.autocapture)}
                });
              `}
            </Script>
          </>
        )}
      </head>
      <body className={`${publicSans.variable} bg-zinc-950 tracking-tight text-white antialiased`}>
        <MixpanelProvider>
          <ErrorBoundary showDetails={process.env.NODE_ENV === 'development'}>
            {/* Analytics components */}
            {ANALYTICS_CONFIG.hotjar.enabled && (
              <Hotjar hjid={ANALYTICS_CONFIG.hotjar.hjid} hjsv={ANALYTICS_CONFIG.hotjar.hjsv} />
            )}
            <div className="flex min-h-screen w-full flex-col font-[family-name:var(--font-public-sans)]">
              <Header />
              <main className="relative mx-auto w-full max-w-screen-lg flex-1 px-3 pt-32">
                {children}
              </main>
              <Footer />
            </div>
          </ErrorBoundary>
        </MixpanelProvider>
      </body>
    </html>
  )
}
