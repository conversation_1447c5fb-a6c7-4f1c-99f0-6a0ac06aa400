import { useRef, useEffect } from 'react'

import { isClient } from '@/utils'

/**
 * Custom hook for adding event listeners
 */
export function useEventListener<K extends keyof WindowEventMap>(
  eventName: K,
  handler: (event: WindowEventMap[K]) => void,
  element?: Element | Window | Document | null,
  options?: boolean | AddEventListenerOptions,
): void {
  const savedHandler = useRef<(event: WindowEventMap[K]) => void>(handler)

  useEffect(() => {
    savedHandler.current = handler
  }, [handler])

  useEffect(() => {
    if (!isClient) return

    const targetElement = element ?? window
    if (!targetElement?.addEventListener) return

    const eventListener = (event: Event) => {
      savedHandler.current?.(event as WindowEventMap[K])
    }

    targetElement.addEventListener(eventName, eventListener, options)

    return () => {
      targetElement.removeEventListener(eventName, eventListener, options)
    }
  }, [eventName, element, options])
}
