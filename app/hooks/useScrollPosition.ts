import { useState, useEffect } from 'react'

import { isClient, throttle } from '@/utils'

interface ScrollPosition {
  x: number
  y: number
}

/**
 * Custom hook to track scroll position
 */
export function useScrollPosition(throttleMs: number = 100): ScrollPosition {
  const [scrollPosition, setScrollPosition] = useState<ScrollPosition>({
    x: 0,
    y: 0,
  })

  useEffect(() => {
    if (!isClient) return

    const updateScrollPosition = throttle(() => {
      setScrollPosition({
        x: window.scrollX,
        y: window.scrollY,
      })
    }, throttleMs)

    window.addEventListener('scroll', updateScrollPosition)

    // Set initial position
    updateScrollPosition()

    return () => {
      window.removeEventListener('scroll', updateScrollPosition)
    }
  }, [throttleMs])

  return scrollPosition
}

/**
 * Hook to detect scroll direction
 */
export function useScrollDirection(
  threshold: number = 10,
): 'up' | 'down' | null {
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(
    null,
  )
  const [lastScrollY, setLastScrollY] = useState(0)

  useEffect(() => {
    if (!isClient) return

    const updateScrollDirection = throttle(() => {
      const scrollY = window.scrollY
      const direction = scrollY > lastScrollY ? 'down' : 'up'

      if (Math.abs(scrollY - lastScrollY) > threshold) {
        setScrollDirection(direction)
        setLastScrollY(scrollY)
      }
    }, 100)

    window.addEventListener('scroll', updateScrollDirection)

    return () => {
      window.removeEventListener('scroll', updateScrollDirection)
    }
  }, [lastScrollY, threshold])

  return scrollDirection
}
