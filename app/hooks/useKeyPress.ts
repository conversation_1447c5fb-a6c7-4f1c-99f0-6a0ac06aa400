import { useState, useEffect } from 'react'

import { isClient } from '@/utils'

/**
 * Custom hook to detect key presses
 */
export function useKeyPress(targetKey: string): boolean {
  const [keyPressed, setKeyPressed] = useState(false)

  useEffect(() => {
    if (!isClient) return

    const downHandler = (event: KeyboardEvent) => {
      if (event.key === targetKey) {
        setKeyPressed(true)
      }
    }

    const upHandler = (event: KeyboardEvent) => {
      if (event.key === targetKey) {
        setKeyPressed(false)
      }
    }

    window.addEventListener('keydown', downHandler)
    window.addEventListener('keyup', upHandler)

    return () => {
      window.removeEventListener('keydown', downHandler)
      window.removeEventListener('keyup', upHandler)
    }
  }, [targetKey])

  return keyPressed
}

/**
 * Hook to detect multiple key combinations
 */
export function useKeyCombo(keys: string[]): boolean {
  const [keysPressed, setKeysPressed] = useState<Set<string>>(new Set())

  useEffect(() => {
    if (!isClient) return

    const downHandler = (event: KeyboardEvent) => {
      setKeysPressed((prev) => new Set([...prev, event.key]))
    }

    const upHandler = (event: KeyboardEvent) => {
      setKeysPressed((prev) => {
        const newSet = new Set(prev)
        newSet.delete(event.key)
        return newSet
      })
    }

    window.addEventListener('keydown', downHandler)
    window.addEventListener('keyup', upHandler)

    return () => {
      window.removeEventListener('keydown', downHandler)
      window.removeEventListener('keyup', upHandler)
    }
  }, [])

  return keys.every((key) => keysPressed.has(key))
}
