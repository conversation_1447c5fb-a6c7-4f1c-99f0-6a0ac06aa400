'use client'

import { useEffect, useRef, useState, useCallback } from 'react'

interface ImagePerformanceMetrics {
  loadStartTime: number | null
  loadEndTime: number | null
  loadDuration: number | null
  intersectionTime: number | null
  timeToIntersection: number | null
  imageSize: { width: number; height: number } | null
  wasIntersecting: boolean
  loadAttempts: number
  errorCount: number
  cacheHit: boolean
}

interface UseImagePerformanceOptions {
  src: string
  trackIntersection?: boolean
  trackCacheHit?: boolean
  onMetricsUpdate?: (metrics: ImagePerformanceMetrics) => void
}

/**
 * Hook for tracking image loading performance metrics
 * Useful for optimizing progressive loading strategies
 */
export function useImagePerformance({
  src,
  trackIntersection = true,
  trackCacheHit = true,
  onMetricsUpdate,
}: UseImagePerformanceOptions) {
  const [metrics, setMetrics] = useState<ImagePerformanceMetrics>({
    loadStartTime: null,
    loadEndTime: null,
    loadDuration: null,
    intersectionTime: null,
    timeToIntersection: null,
    imageSize: null,
    wasIntersecting: false,
    loadAttempts: 0,
    errorCount: 0,
    cacheHit: false,
  })

  const mountTime = useRef<number>(Date.now())
  const intersectionObserver = useRef<IntersectionObserver | null>(null)
  const elementRef = useRef<Element | null>(null)

  // Update metrics and notify callback
  const updateMetrics = useCallback(
    (updates: Partial<ImagePerformanceMetrics>) => {
      setMetrics((prev) => {
        const newMetrics = { ...prev, ...updates }
        onMetricsUpdate?.(newMetrics)
        return newMetrics
      })
    },
    [onMetricsUpdate],
  )

  // Track intersection
  const trackIntersectionTime = useCallback(() => {
    if (!trackIntersection) return

    const intersectionTime = Date.now()
    const timeToIntersection = intersectionTime - mountTime.current

    updateMetrics({
      intersectionTime,
      timeToIntersection,
      wasIntersecting: true,
    })
  }, [trackIntersection, updateMetrics])

  // Set up intersection observer
  useEffect(() => {
    if (!trackIntersection) return

    intersectionObserver.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !metrics.wasIntersecting) {
            trackIntersectionTime()
          }
        })
      },
      { threshold: 0.1 },
    )

    return () => {
      intersectionObserver.current?.disconnect()
    }
  }, [trackIntersection, trackIntersectionTime, metrics.wasIntersecting])

  // Check if image is cached
  const checkCacheHit = useCallback(
    async (imageSrc: string): Promise<boolean> => {
      if (!trackCacheHit) return false

      try {
        // Create a new image to test cache
        const testImg = new Image()
        const startTime = performance.now()

        return new Promise((resolve) => {
          testImg.onload = () => {
            const loadTime = performance.now() - startTime
            // If load time is very fast (< 10ms), likely cached
            resolve(loadTime < 10)
          }
          testImg.onerror = () => resolve(false)
          testImg.src = imageSrc
        })
      } catch {
        return false
      }
    },
    [trackCacheHit],
  )

  // Image loading handlers
  const handleLoadStart = useCallback(() => {
    const loadStartTime = Date.now()
    updateMetrics({
      loadStartTime,
      loadAttempts: metrics.loadAttempts + 1,
    })

    // Check cache hit
    if (trackCacheHit) {
      checkCacheHit(src).then((cacheHit) => {
        updateMetrics({ cacheHit })
      })
    }
  }, [updateMetrics, metrics.loadAttempts, trackCacheHit, checkCacheHit, src])

  const handleLoad = useCallback(
    (event?: Event) => {
      const loadEndTime = Date.now()
      const loadDuration = metrics.loadStartTime
        ? loadEndTime - metrics.loadStartTime
        : null

      let imageSize = null
      if (event?.target instanceof HTMLImageElement) {
        imageSize = {
          width: event.target.naturalWidth,
          height: event.target.naturalHeight,
        }
      }

      updateMetrics({
        loadEndTime,
        loadDuration,
        imageSize,
      })
    },
    [updateMetrics, metrics.loadStartTime],
  )

  const handleError = useCallback(() => {
    updateMetrics({
      errorCount: metrics.errorCount + 1,
    })
  }, [updateMetrics, metrics.errorCount])

  // Ref callback for intersection observer
  const ref = useCallback((element: Element | null) => {
    if (elementRef.current && intersectionObserver.current) {
      intersectionObserver.current.unobserve(elementRef.current)
    }

    elementRef.current = element

    if (element && intersectionObserver.current) {
      intersectionObserver.current.observe(element)
    }
  }, [])

  // Reset metrics when src changes
  useEffect(() => {
    setMetrics({
      loadStartTime: null,
      loadEndTime: null,
      loadDuration: null,
      intersectionTime: null,
      timeToIntersection: null,
      imageSize: null,
      wasIntersecting: false,
      loadAttempts: 0,
      errorCount: 0,
      cacheHit: false,
    })
    mountTime.current = Date.now()
  }, [src])

  return {
    metrics,
    handlers: {
      onLoadStart: handleLoadStart,
      onLoad: handleLoad,
      onError: handleError,
    },
    ref: trackIntersection ? ref : undefined,
  }
}

/**
 * Hook for aggregating performance metrics across multiple images
 */
export function useImagePerformanceAggregator() {
  const [aggregatedMetrics, setAggregatedMetrics] = useState({
    totalImages: 0,
    loadedImages: 0,
    failedImages: 0,
    averageLoadTime: 0,
    averageTimeToIntersection: 0,
    cacheHitRate: 0,
    totalLoadTime: 0,
    totalIntersectionTime: 0,
    cacheHits: 0,
  })

  const addMetrics = useCallback((metrics: ImagePerformanceMetrics) => {
    setAggregatedMetrics((prev) => {
      const newTotal = prev.totalImages + 1
      const newLoaded =
        metrics.loadDuration !== null
          ? prev.loadedImages + 1
          : prev.loadedImages
      const newFailed =
        metrics.errorCount > 0 ? prev.failedImages + 1 : prev.failedImages
      const newTotalLoadTime = prev.totalLoadTime + (metrics.loadDuration || 0)
      const newTotalIntersectionTime =
        prev.totalIntersectionTime + (metrics.timeToIntersection || 0)
      const newCacheHits = prev.cacheHits + (metrics.cacheHit ? 1 : 0)

      return {
        totalImages: newTotal,
        loadedImages: newLoaded,
        failedImages: newFailed,
        averageLoadTime: newLoaded > 0 ? newTotalLoadTime / newLoaded : 0,
        averageTimeToIntersection:
          newTotal > 0 ? newTotalIntersectionTime / newTotal : 0,
        cacheHitRate: newTotal > 0 ? (newCacheHits / newTotal) * 100 : 0,
        totalLoadTime: newTotalLoadTime,
        totalIntersectionTime: newTotalIntersectionTime,
        cacheHits: newCacheHits,
      }
    })
  }, [])

  const reset = useCallback(() => {
    setAggregatedMetrics({
      totalImages: 0,
      loadedImages: 0,
      failedImages: 0,
      averageLoadTime: 0,
      averageTimeToIntersection: 0,
      cacheHitRate: 0,
      totalLoadTime: 0,
      totalIntersectionTime: 0,
      cacheHits: 0,
    })
  }, [])

  return {
    metrics: aggregatedMetrics,
    addMetrics,
    reset,
  }
}

/**
 * Hook for monitoring Core Web Vitals related to images
 */
export function useImageWebVitals() {
  const [vitals, setVitals] = useState({
    lcp: null as number | null,
    cls: null as number | null,
    fid: null as number | null,
  })

  useEffect(() => {
    // Monitor Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1] as any
      if (lastEntry) {
        setVitals((prev) => ({ ...prev, lcp: lastEntry.startTime }))
      }
    })

    // Monitor Cumulative Layout Shift
    const clsObserver = new PerformanceObserver((list) => {
      let clsValue = 0
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value
        }
      }
      setVitals((prev) => ({ ...prev, cls: clsValue }))
    })

    // Monitor First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        setVitals((prev) => ({
          ...prev,
          fid: (entry as any).processingStart - entry.startTime,
        }))
      }
    })

    try {
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      clsObserver.observe({ entryTypes: ['layout-shift'] })
      fidObserver.observe({ entryTypes: ['first-input'] })
    } catch (error) {
      console.warn('Performance observers not supported:', error)
    }

    return () => {
      lcpObserver.disconnect()
      clsObserver.disconnect()
      fidObserver.disconnect()
    }
  }, [])

  return vitals
}
