import { useState, useEffect } from 'react'

import { isClient } from '@/utils'

/**
 * Custom hook for responsive design with media queries
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState<boolean>(() => {
    if (!isClient) return false
    return window.matchMedia(query).matches
  })

  useEffect(() => {
    if (!isClient) return

    const mediaQuery = window.matchMedia(query)

    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    // Set initial value
    setMatches(mediaQuery.matches)

    // Listen for changes
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange)
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleChange)
    }

    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange)
      } else {
        // Fallback for older browsers
        mediaQuery.removeListener(handleChange)
      }
    }
  }, [query])

  return matches
}

/**
 * Predefined breakpoint hooks
 */
export const useIsMobile = () => useMediaQuery('(max-width: 767px)')
export const useIsTablet = () =>
  useMediaQuery('(min-width: 768px) and (max-width: 1023px)')
export const useIsDesktop = () => useMediaQuery('(min-width: 1024px)')
export const useIsLargeDesktop = () => useMediaQuery('(min-width: 1280px)')

/**
 * Dark mode preference hook
 */
export const usePrefersDarkMode = () =>
  useMediaQuery('(prefers-color-scheme: dark)')

/**
 * Reduced motion preference hook
 */
export const usePrefersReducedMotion = () =>
  useMediaQuery('(prefers-reduced-motion: reduce)')

/**
 * High contrast preference hook
 */
export const usePrefersHighContrast = () =>
  useMediaQuery('(prefers-contrast: high)')

/**
 * Hover capability hook
 */
export const useCanHover = () => useMediaQuery('(hover: hover)')

/**
 * Touch capability hook
 */
export const useIsTouchDevice = () => useMediaQuery('(pointer: coarse)')
