import { useState, useEffect } from 'react'

import { isClient, throttle } from '@/utils'

interface WindowSize {
  width: number
  height: number
}

/**
 * Custom hook to track window size
 */
export function useWindowSize(throttleMs: number = 100): WindowSize {
  const [windowSize, setWindowSize] = useState<WindowSize>(() => {
    if (!isClient) {
      return { width: 0, height: 0 }
    }
    return {
      width: window.innerWidth,
      height: window.innerHeight,
    }
  })

  useEffect(() => {
    if (!isClient) return

    const handleResize = throttle(() => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }, throttleMs)

    window.addEventListener('resize', handleResize)

    // Set initial size
    handleResize()

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [throttleMs])

  return windowSize
}
