'use client'

import { useEffect, useRef, useState, useCallback } from 'react'

interface UseIntersectionObserverOptions {
  threshold?: number | number[]
  rootMargin?: string
  root?: Element | null
  triggerOnce?: boolean
  skip?: boolean
}

interface UseIntersectionObserverReturn {
  isIntersecting: boolean
  entry: IntersectionObserverEntry | null
  ref: (element: Element | null) => void
}

/**
 * Enhanced useIntersectionObserver hook for progressive loading
 * Provides better performance and more control over intersection detection
 */
export function useIntersectionObserver({
  threshold = 0.1,
  rootMargin = '50px',
  root = null,
  triggerOnce = true,
  skip = false,
}: UseIntersectionObserverOptions = {}): UseIntersectionObserverReturn {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null)
  const elementRef = useRef<Element | null>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)

  const setRef = useCallback((element: Element | null) => {
    elementRef.current = element
  }, [])

  useEffect(() => {
    if (skip) return

    const element = elementRef.current
    if (!element) return

    // If already intersecting and triggerOnce is true, don't observe again
    if (isIntersecting && triggerOnce) return

    // Create observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        setEntry(entry)
        setIsIntersecting(entry.isIntersecting)

        // If triggerOnce is true and element is intersecting, disconnect
        if (entry.isIntersecting && triggerOnce) {
          observerRef.current?.disconnect()
        }
      },
      {
        threshold,
        rootMargin,
        root,
      },
    )

    observerRef.current.observe(element)

    return () => {
      observerRef.current?.disconnect()
    }
  }, [threshold, rootMargin, root, triggerOnce, skip, isIntersecting])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      observerRef.current?.disconnect()
    }
  }, [])

  return {
    isIntersecting,
    entry,
    ref: setRef,
  }
}

/**
 * Hook for preloading images that are about to enter the viewport
 * Useful for smoother user experience
 */
export function useImagePreloader(
  src: string,
  options: UseIntersectionObserverOptions = {},
) {
  const [isPreloaded, setIsPreloaded] = useState(false)
  const { isIntersecting, ref } = useIntersectionObserver({
    rootMargin: '200px', // Start preloading when image is 200px away
    triggerOnce: true,
    ...options,
  })

  useEffect(() => {
    if (!isIntersecting || isPreloaded) return

    const img = new Image()
    img.onload = () => setIsPreloaded(true)
    img.onerror = () => setIsPreloaded(false)
    img.src = src
  }, [isIntersecting, src, isPreloaded])

  return {
    isPreloaded,
    isIntersecting,
    ref,
  }
}

/**
 * Hook for lazy loading with multiple thresholds
 * Useful for progressive enhancement
 */
export function useLazyLoad(options: UseIntersectionObserverOptions = {}) {
  const [loadingState, setLoadingState] = useState<
    'idle' | 'loading' | 'loaded' | 'error'
  >('idle')

  const { isIntersecting, entry, ref } = useIntersectionObserver({
    threshold: [0, 0.25, 0.5, 0.75, 1],
    rootMargin: '100px',
    triggerOnce: false,
    ...options,
  })

  const startLoading = useCallback(() => {
    setLoadingState('loading')
  }, [])

  const markLoaded = useCallback(() => {
    setLoadingState('loaded')
  }, [])

  const markError = useCallback(() => {
    setLoadingState('error')
  }, [])

  return {
    isIntersecting,
    entry,
    ref,
    loadingState,
    startLoading,
    markLoaded,
    markError,
    intersectionRatio: entry?.intersectionRatio || 0,
  }
}
