import { MetadataRoute } from 'next'
import { WEBSITE_URL } from '@/lib/constants'
import { getAllPosts } from '@/lib/posts'
import fs from 'node:fs'
import path from 'node:path'
import matter from 'gray-matter'

export const dynamic = 'force-static'

// Get all work items for sitemap
function getAllWorkItems() {
  try {
    const workDirectory = path.join(process.cwd(), '_work')

    if (!fs.existsSync(workDirectory)) {
      return []
    }

    const fileNames = fs
      .readdirSync(workDirectory)
      .filter((fileName) => fileName.endsWith('.md'))

    return fileNames.map((fileName) => {
      const slug = fileName.replace(/\.md$/, '')
      const fullPath = path.join(workDirectory, fileName)
      const fileContents = fs.readFileSync(fullPath, 'utf8')
      const { data } = matter(fileContents)

      return {
        slug,
        date: data.date ? String(data.date) : new Date().toISOString().split('T')[0],
      }
    })
  } catch (error) {
    console.error('Error loading work items for sitemap:', error)
    return []
  }
}

export default function sitemap(): MetadataRoute.Sitemap {
  const basePages = [
    {
      url: WEBSITE_URL,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 1,
    },
    {
      url: `${WEBSITE_URL}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.9,
    },
    {
      url: `${WEBSITE_URL}/blog`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
  ]

  // Get all blog posts
  const posts = getAllPosts()
  const blogPages = posts.map((post) => ({
    url: `${WEBSITE_URL}/blog/${post.slug}`,
    lastModified: new Date(post.date),
    changeFrequency: 'monthly' as const,
    priority: 0.7,
  }))

  // Get all work items
  const workItems = getAllWorkItems()
  const workPages = workItems.map((work) => ({
    url: `${WEBSITE_URL}/work/${work.slug}`,
    lastModified: new Date(work.date),
    changeFrequency: 'monthly' as const,
    priority: 0.8,
  }))

  return [...basePages, ...blogPages, ...workPages]
}
