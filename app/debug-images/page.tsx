import { ResponsiveImage } from '@/components/ResponsiveImage'
import Image from 'next/image'

export default function DebugImages() {
  return (
    <div className="mx-auto max-w-4xl px-4 py-8">
      <h1 className="mb-8 text-3xl font-bold">Image Debug Comparison</h1>
      
      <div className="space-y-12">
        {/* Standard Next.js Image */}
        <section>
          <h2 className="mb-4 text-xl font-semibold">Standard Next.js Image</h2>
          <p className="mb-4 text-sm text-gray-600">
            Using default Next.js Image component with custom loader
          </p>
          <div className="border-2 border-blue-200 p-4">
            <Image
              src="/images/work_elevate_cover.jpg"
              alt="Standard Next.js Image"
              width={800}
              height={450}
              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 800px"
              className="rounded-lg"
            />
          </div>
          <div className="mt-2 text-xs text-gray-500">
            This should show the srcset generated by Next.js using our custom loader
          </div>
        </section>

        {/* ResponsiveImage Component */}
        <section>
          <h2 className="mb-4 text-xl font-semibold">ResponsiveImage Component</h2>
          <p className="mb-4 text-sm text-gray-600">
            Using our custom ResponsiveImage component with context detection
          </p>
          <div className="border-2 border-green-200 p-4">
            <ResponsiveImage
              src="/images/work_elevate_cover.jpg"
              alt="ResponsiveImage Component"
              width={800}
              height={450}
              context="regular"
              className="rounded-lg"
            />
          </div>
          <div className="mt-2 text-xs text-gray-500">
            This should show our enhanced responsive image with context-aware sizing
          </div>
        </section>

        {/* Auto-detect Context */}
        <section>
          <h2 className="mb-4 text-xl font-semibold">Auto-detect Context</h2>
          <p className="mb-4 text-sm text-gray-600">
            ResponsiveImage with automatic context detection
          </p>
          <div className="border-2 border-purple-200 p-4">
            <ResponsiveImage
              src="/images/work_elevate_cover.jpg"
              alt="Auto-detect Context"
              width={1400}
              height={788}
              autoDetectContext={true}
              className="rounded-lg"
            />
          </div>
          <div className="mt-2 text-xs text-gray-500">
            This should auto-detect as 'wide' context based on dimensions
          </div>
        </section>

        {/* Thumbnail Context */}
        <section>
          <h2 className="mb-4 text-xl font-semibold">Thumbnail Context</h2>
          <p className="mb-4 text-sm text-gray-600">
            ResponsiveImage optimized for thumbnail usage
          </p>
          <div className="grid grid-cols-3 gap-4">
            <div className="border-2 border-orange-200 p-2">
              <ResponsiveImage
                src="/images/work_elevate_cover.jpg"
                alt="Thumbnail 1"
                width={400}
                height={225}
                context="thumbnail"
                className="rounded-lg"
              />
            </div>
            <div className="border-2 border-orange-200 p-2">
              <ResponsiveImage
                src="/images/work_citysuper_cover.jpg"
                alt="Thumbnail 2"
                width={400}
                height={225}
                context="thumbnail"
                className="rounded-lg"
              />
            </div>
            <div className="border-2 border-orange-200 p-2">
              <ResponsiveImage
                src="/images/work_parknshop_cover.jpg"
                alt="Thumbnail 3"
                width={400}
                height={225}
                context="thumbnail"
                className="rounded-lg"
              />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            These should use thumbnail-optimized sizes and responsive behavior
          </div>
        </section>

        {/* Debug Information */}
        <section>
          <h2 className="mb-4 text-xl font-semibold">Debug Information</h2>
          <div className="rounded-lg bg-gray-100 p-4 text-sm">
            <h3 className="font-semibold mb-2">Expected Behavior:</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>Images should load AVIF format when available</li>
              <li>Srcsets should contain multiple width variants</li>
              <li>Sizes attributes should be context-appropriate</li>
              <li>No 404 errors in network tab</li>
              <li>Progressive loading with blur placeholders</li>
            </ul>
            
            <h3 className="font-semibold mb-2 mt-4">To Debug:</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>Open browser DevTools → Network tab</li>
              <li>Refresh page and check image requests</li>
              <li>Inspect HTML to see generated srcset attributes</li>
              <li>Check console for any errors</li>
            </ul>
          </div>
        </section>
      </div>
    </div>
  )
}
