'use client';

import { <PERSON>UpR<PERSON>, <PERSON>u, X } from 'lucide-react';
import { animate } from 'motion';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

import { ANIMATION_CONFIG, NAVIGATION, SITE_CONFIG } from '@/constants';
import { cn, scrollToElement } from '@/utils';

export function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // Refs for navigation links
  const desktopNavRefs = useRef<Record<string, HTMLAnchorElement | null>>({});
  const mobileNavRefs = useRef<Record<string, HTMLAnchorElement | null>>({});

  // Set refs for navigation items
  const setDesktopRef = (key: string) => (el: HTMLAnchorElement | null) => {
    desktopNavRefs.current[key] = el;
  };

  const setMobileRef = (key: string) => (el: HTMLAnchorElement | null) => {
    mobileNavRefs.current[key] = el;
  };

  // Apply hover animations
  useEffect(() => {
    const applyHoverAnimation = (element: HTMLElement) => {
      const handleMouseEnter = () => {
        animate(
          element.style,
          { color: '#ffffff' },
          {
            duration: ANIMATION_CONFIG.duration.normal,
            ease: ANIMATION_CONFIG.ease.default
          }
        );
      };

      const handleMouseLeave = () => {
        animate(
          element.style,
          { color: '#71717a' },
          {
            duration: ANIMATION_CONFIG.duration.normal,
            ease: ANIMATION_CONFIG.ease.default
          }
        );
      };

      element.addEventListener('mouseenter', handleMouseEnter);
      element.addEventListener('mouseleave', handleMouseLeave);

      // Return cleanup function
      return () => {
        element.removeEventListener('mouseenter', handleMouseEnter);
        element.removeEventListener('mouseleave', handleMouseLeave);
      };
    };

    const cleanupFunctions: (() => void)[] = [];

    // Apply to desktop nav links
    Object.values(desktopNavRefs.current).forEach((ref) => {
      if (ref) {
        const cleanup = applyHoverAnimation(ref);
        if (cleanup) cleanupFunctions.push(cleanup);
      }
    });

    // Apply to mobile nav links
    Object.values(mobileNavRefs.current).forEach((ref) => {
      if (ref) {
        const cleanup = applyHoverAnimation(ref);
        if (cleanup) cleanupFunctions.push(cleanup);
      }
    });

    // Cleanup on unmount
    return () => {
      cleanupFunctions.forEach((cleanup) => cleanup());
    };
  }, []);

  const handleWorkClick = (e: React.MouseEvent) => {
    // Only handle smooth scrolling on the homepage
    if (pathname === '/') {
      e.preventDefault();
      scrollToElement('work', 80); // Use utility function with header offset
    } else {
      // Navigate to the homepage with the hash if on a different page
      router.push('/#work');
    }

    // Close mobile menu if open
    if (mobileMenuOpen) {
      setMobileMenuOpen(false);
    }
  };

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
  };

  return (
    <header
      className={cn(
        'fixed top-0 right-0 left-0 z-50 w-full bg-zinc-950',
        'border-b border-zinc-800 py-3'
      )}
    >
      <div className="mx-auto flex w-full max-w-[1632px] items-center justify-between px-3">
        <div className="flex items-center space-x-20 lg:space-x-40">
          <div className="flex items-center space-x-4">
            <Link
              href="/"
              className="font-medium text-white transition-opacity hover:opacity-80"
            >
              {SITE_CONFIG.name}
            </Link>
            <div className="hidden text-zinc-400 sm:block">{SITE_CONFIG.role}</div>
          </div>
          <div className="hidden text-zinc-400 lg:block">{SITE_CONFIG.location}</div>
        </div>

        {/* Desktop navigation */}
        <div className="hidden items-center space-x-6 md:flex">
          <nav className="flex items-center space-x-8">
            {NAVIGATION.main.map((item) => {
              const isExternal = 'external' in item && item.external;
              return (
                <Link
                  key={item.href}
                  ref={setDesktopRef(item.label)}
                  href={item.href}
                  className={cn(
                    'text-zinc-400 transition-colors hover:text-white',
                    isExternal && 'flex items-center'
                  )}
                  {...(isExternal
                    ? {
                        target: '_blank',
                        rel: 'noopener noreferrer',
                      }
                    : {})}
                >
                  {item.label}
                  {isExternal && <ArrowUpRight className="ml-1 h-3 w-3" />}
                </Link>
              );
            })}
          </nav>
        </div>

        {/* Mobile menu button */}
        <button
          type="button"
          className={cn(
            'text-zinc-400 hover:text-white md:hidden',
            'transition-colors duration-200',
          )}
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          aria-label="Toggle mobile menu"
        >
          <Menu className="h-6 w-6" />
        </button>
      </div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-zinc-950 md:hidden">
          {/* Close button */}
          <button
            type="button"
            className={cn(
              'absolute top-4 right-4 text-zinc-400 hover:text-white',
              'transition-colors duration-200',
            )}
            onClick={closeMobileMenu}
            aria-label="Close mobile menu"
          >
            <X className="h-6 w-6" />
          </button>

          <nav className="flex flex-col items-center space-y-8">
            <Link
              href="/#work"
              ref={setMobileRef('work')}
              className="cursor-pointer text-2xl text-zinc-400 transition-colors hover:text-white"
              onClick={handleWorkClick}
            >
              work
            </Link>
            {NAVIGATION.main.map((item) => {
              const isExternal = 'external' in item && item.external;
              return (
                <Link
                  key={item.href}
                  ref={setMobileRef(item.label)}
                  href={item.href}
                  className={cn(
                    'text-2xl text-zinc-400 transition-colors hover:text-white',
                    isExternal && 'flex items-center'
                  )}
                  onClick={closeMobileMenu}
                  {...(isExternal
                    ? {
                        target: '_blank',
                        rel: 'noopener noreferrer',
                      }
                    : {})}
                >
                  {item.label}
                  {isExternal && <ArrowUpRight className="ml-1 h-4 w-4" />}
                </Link>
              );
            })}
          </nav>
        </div>
      )}
    </header>
  );
}
