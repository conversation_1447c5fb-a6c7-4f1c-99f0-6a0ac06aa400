'use client'
import { StaticImageData } from 'next/image'
import Link from 'next/link'
import { IMAGE_CONTEXTS } from '@/lib/imageUtils'
import { WorkItem } from '@/app/data/works'

// Process image URL to ensure it points to the correct location
function getProcessedImageUrl(
  url: string | StaticImageData,
): string | StaticImageData {
  if (typeof url !== 'string') return url

  // If it's already a StaticImageData or starts with /images/, return as is
  if (url.startsWith('/images/')) return url

  // If it starts with / but not with /images/, add /images prefix
  if (url.startsWith('/') && !url.startsWith('/images/')) {
    return `/images${url}`
  }

  // If it doesn't start with /, prepend /images/
  if (!url.startsWith('/')) {
    return `/images/${url}`
  }

  return url
}

// Generate DPI-optimized image with proper srcset (Intercom style)
function generateDPIOptimizedImage(src: string, alt: string): JSX.Element {
  // Get the base path without extension
  const basePath = src.replace(/\.[^.]+$/, '')

  // Use thumbnail context for WorkSection images
  const contextConfig = IMAGE_CONTEXTS.thumbnail
  const contextDPI = contextConfig.dpi

  // Generate DPI-based srcset (Intercom style)
  const srcset = contextDPI
    .map((dpr) => {
      const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
      return `${basePath}${dpiInfo}.avif ${dpr}x`
    })
    .join(', ')

  // Main src uses 1x variant
  const mainSrc = `${basePath}_1x.avif`

  return (
    <img
      src={mainSrc}
      srcSet={srcset}
      alt={alt}
      className="h-full w-full object-cover object-center transition-all duration-500 ease-out group-hover:opacity-50"
      style={{ width: '100%', height: '100%' }}
      loading="lazy"
      decoding="async"
    />
  )
}

// Component for displaying work thumbnails
function WorkThumb({ work }: { work: WorkItem }) {
  if (!work.coverImage) {
    return null
  }

  // Process the URL to ensure it points to the correct location
  const processedUrl = getProcessedImageUrl(work.coverImage)

  // Handle StaticImageData (imported images)
  if (typeof processedUrl !== 'string') {
    return (
      <div className="group relative aspect-video w-full overflow-hidden rounded-sm">
        <img
          src={processedUrl.src}
          alt={work.title}
          className="h-full w-full object-cover object-center transition-all duration-500 ease-out group-hover:opacity-50"
          style={{ width: '100%', height: '100%' }}
          loading="lazy"
          decoding="async"
        />

        {/* Arrow overlay that appears on hover */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-black/70 text-white">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M5 12h14"></path>
              <path d="m12 5 7 7-7 7"></path>
            </svg>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="group relative aspect-video w-full overflow-hidden rounded-sm">
      {generateDPIOptimizedImage(processedUrl, work.title)}

      {/* Arrow overlay that appears on hover */}
      <div className="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-black/70 text-white">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </div>
      </div>
    </div>
  )
}

// Main WorkSection component that displays all work items
export function WorkSection({ works }: { works: WorkItem[] }) {
  // Filter works to only show items with showOnHomepage flag set to true (or undefined, defaulting to true)
  const visibleWorks = works.filter((work) => work.showOnHomepage !== false)

  return (
    <>
      <div className="mb-30 grid grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2">
        {visibleWorks.map((work) => (
          <Link
            key={work.slug}
            href={`/work/${work.slug}`}
            className="block space-y-2"
          >
            <div className="relative overflow-hidden rounded-sm bg-zinc-50/40 p-1 ring-1 ring-zinc-200/50 ring-inset dark:bg-zinc-950/40 dark:ring-zinc-800/50">
              <WorkThumb work={work} />
            </div>
            <div className="px-1">
              <div className="font-base relative inline-block font-[450] text-zinc-900 dark:text-zinc-50">
                {work.title}
              </div>
              {work.subtitle && (
                <p className="text-sm text-zinc-700 dark:text-zinc-200">
                  {work.subtitle}
                </p>
              )}
            </div>
          </Link>
        ))}
      </div>
    </>
  )
}
