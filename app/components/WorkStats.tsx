'use client'

import React from 'react'
import parse from 'html-react-parser'

type StatItem = {
  value: string
  description: string | React.ReactNode
}

type WorkStatsProps = {
  items: StatItem[]
}

export function WorkStats({ items }: WorkStatsProps) {
  // Determine grid layout based on number of items
  const getGridCols = () => {
    if (items.length === 2) {
      return 'grid-cols-1 md:grid-cols-2'
    } else if (items.length === 3) {
      return 'grid-cols-1 md:grid-cols-3'
    } else {
      // For more than 3 items, use 3 columns max and allow stacking
      return 'grid-cols-1 md:grid-cols-3'
    }
  }

  return (
    <div className="my-8">
      <div className={`grid ${getGridCols()} gap-2 md:gap-3`}>
        {items.map((item, index) => (
          <div
            key={index}
            className="flex flex-col space-y-3 rounded-sm bg-zinc-100 p-4 dark:bg-zinc-900"
          >
            {/* Large stat value/percentage */}
            <div className="font-regular mb-1 text-3xl text-zinc-900 md:text-3xl dark:text-zinc-50">
              {item.value}
            </div>
            {/* Description text */}
            <div className="text-sm leading-relaxed text-zinc-600 md:text-base dark:text-zinc-400">
              {typeof item.description === 'string' ? (
                <p className="mt-0 mb-0">{parse(item.description as string)}</p>
              ) : (
                item.description
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
