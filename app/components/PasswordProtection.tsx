'use client'

import React, { useState, useEffect } from 'react'

type PasswordProtectionProps = {
  password: string
  children: React.ReactNode
}

export function PasswordProtection({
  password,
  children,
}: PasswordProtectionProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [inputPassword, setInputPassword] = useState('')
  const [error, setError] = useState('')

  // Check if already authenticated in localStorage
  useEffect(() => {
    const storedAuth = localStorage.getItem(`password_auth_${password}`)
    if (storedAuth === 'true') {
      setIsAuthenticated(true)
    }
  }, [password])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (inputPassword === password) {
      // Save authentication state to localStorage
      localStorage.setItem(`password_auth_${password}`, 'true')
      setIsAuthenticated(true)
      setError('')
    } else {
      setError('Incorrect password. Please try again.')
    }
  }

  return (
    <div className="relative">
      {/* Always render the content but hide it behind overlay if not authenticated */}
      <div className={isAuthenticated ? 'block' : 'hidden'}>{children}</div>

      {/* Password overlay */}
      {!isAuthenticated && (
        <div className="flex min-h-[50vh] flex-col items-center justify-center px-4 py-12">
          <div className="w-full max-w-md space-y-8 rounded-lg bg-white p-8 shadow-md dark:bg-zinc-900">
            <div>
              <h2 className="text-center text-2xl font-bold text-gray-900 dark:text-white">
                Password Protected Content
              </h2>
              <p className="mt-2 text-center text-sm text-zinc-700 dark:text-zinc-200">
                Please enter the password to view this content
              </p>
            </div>
            <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
              <div>
                <label htmlFor="password" className="sr-only">
                  Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  className="relative block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm dark:border-zinc-700 dark:bg-zinc-800 dark:text-white dark:placeholder-zinc-400"
                  placeholder="Password"
                  value={inputPassword}
                  onChange={(e) => setInputPassword(e.target.value)}
                />
              </div>

              {error && (
                <div className="text-center text-sm text-red-500">{error}</div>
              )}

              <div>
                <button
                  type="submit"
                  className="flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none dark:ring-offset-zinc-900"
                >
                  Enter
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
