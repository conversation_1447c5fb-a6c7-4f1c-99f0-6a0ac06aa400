'use client'

import React from 'react'
import { normalizeMediaPath, IMAGE_CONTEXTS } from '@/lib/imageUtils'

type ImageTextSideBySideProps = {
  imageSrc: string
  imageAlt: string
  title?: string
  description: string
  imagePosition: 'left' | 'right'
  isWide?: boolean
  isMid?: boolean
}

export function ImageTextSideBySide({
  imageSrc,
  imageAlt,
  title,
  description,
  imagePosition = 'left',
  isWide = false,
  isMid = false,
}: ImageTextSideBySideProps) {
  // Process image source
  const processedSrc = normalizeMediaPath(imageSrc)
  
  // Check if this is a video file
  const isVideo = processedSrc.match(/\.(mp4|webm|ogg|mov)($|\?)/i)
  
  // Determine container classes based on size
  const containerClass = isWide
    ? 'wide-image' // Use existing wide-image class for full viewport behavior
    : isMid
      ? 'mid-image' // Use existing mid-image class for 1200px max behavior
      : 'max-w-[1200px] mx-auto' // Default: centered with 1200px max-width

  // Determine image context for optimization
  const imageContext = isWide ? 'wide' : isMid ? 'mid' : 'regular'
  const contextConfig = IMAGE_CONTEXTS[imageContext]

  // Generate DPI-optimized srcset
  const generateDPISrcSet = (src: string) => {
    const basePath = src.replace(/\.[^.]+$/, '')
    return contextConfig.dpi
      .map((dpr) => {
        const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
        return `${basePath}${dpiInfo}.avif ${dpr}x`
      })
      .join(', ')
  }

  // Generate main src (1x variant)
  const getMainSrc = (src: string) => {
    const basePath = src.replace(/\.[^.]+$/, '')
    return `${basePath}_1x.avif`
  }

  // Generate sizes attribute for responsive images
  const getSizesAttribute = () => {
    if (isWide) {
      return '(max-width: 768px) 100vw, 50vw'
    } else if (isMid) {
      return '(max-width: 768px) 100vw, 50vw'
    } else {
      return '(max-width: 768px) 100vw, 50vw'
    }
  }

  const imageElement = isVideo ? (
    <video
      src={processedSrc}
      autoPlay
      loop
      muted
      playsInline
      className="w-full h-auto rounded-sm transition-all duration-500 ease-out"
    >
      <p>{imageAlt || 'Your browser does not support the video tag.'}</p>
    </video>
  ) : (
    <img
      src={getMainSrc(processedSrc)}
      srcSet={generateDPISrcSet(processedSrc)}
      alt={imageAlt}
      sizes={getSizesAttribute()}
      className="w-full h-auto rounded-sm transition-all duration-500 ease-out"
      loading="lazy"
      decoding="async"
    />
  )

  const textContent = (
    <div className="flex flex-col justify-center">
      {title && (
        <h3 className="text-2xl font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
          {title}
        </h3>
      )}
      <div className="text-base leading-relaxed text-zinc-600 dark:text-zinc-400">
        {description}
      </div>
    </div>
  )

  return (
    <div className={`${containerClass} my-12`}>
      <div className="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
        {imagePosition === 'left' ? (
          <>
            <div className="flex-1">
              {imageElement}
            </div>
            <div className="flex-1">
              {textContent}
            </div>
          </>
        ) : (
          <>
            <div className="flex-1 lg:order-2">
              {imageElement}
            </div>
            <div className="flex-1 lg:order-1">
              {textContent}
            </div>
          </>
        )}
      </div>
    </div>
  )
}
