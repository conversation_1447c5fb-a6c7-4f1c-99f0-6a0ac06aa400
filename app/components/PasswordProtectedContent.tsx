'use client'

import React, { useEffect } from 'react'
import { PasswordProtection } from './PasswordProtection'
import { MDXContent } from './MDXContent'

export default function PasswordProtectedContent() {
  useEffect(() => {
    // Find all password protected divs - only the ones without the 'data-processed' attribute
    const protectedDivs = document.querySelectorAll(
      '[data-password-protected="true"]:not([data-processed="true"])',
    )

    protectedDivs.forEach((div) => {
      const password = div.getAttribute('data-password')
      const contentId = div.getAttribute('data-content-id')
      const isProtected = div.getAttribute('data-is-protected') === 'true'

      // Mark this div as processed to avoid duplicate processing
      div.setAttribute('data-processed', 'true')

      if (!password) return

      // For protected content in production, we need to get the content from the hidden script tag
      let workContent = div.getAttribute('data-content')

      // If we have a contentId and this is protected content, look for the script tag with the actual content
      if (contentId && isProtected) {
        const contentScript =
          document.getElementById(contentId) ||
          document.getElementById(`content-${contentId}`)
        if (contentScript && contentScript.textContent) {
          try {
            // Parse the JSON content from the script tag
            const parsedContent = JSON.parse(contentScript.textContent)
            if (parsedContent && parsedContent.content) {
              workContent = parsedContent.content
            }
          } catch (error) {
            console.error('Error parsing content:', error)
          }
        }
      }

      if (!workContent) return

      // Render the password protection component
      const root = document.createElement('div')
      div.appendChild(root)

      // Use React to render the password component
      import('react-dom/client').then(({ createRoot }) => {
        const reactRoot = createRoot(root)
        reactRoot.render(
          <PasswordProtection password={password}>
            {workContent && <MDXContent content={workContent} />}
          </PasswordProtection>,
        )
      })
    })
  }, [])

  // This component doesn't render anything itself
  return null
}
