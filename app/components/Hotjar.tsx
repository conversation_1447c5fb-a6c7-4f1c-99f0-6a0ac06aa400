'use client'

import { useEffect } from 'react'

interface HotjarProps {
  hjid: string
  hjsv: string
}

export default function Hotjar({ hjid, hjsv }: HotjarProps) {
  useEffect(() => {
    // Hotjar is enabled/disabled via ANALYTICS_CONFIG in constants/index.ts

    // Add your Hotjar tracking code here
    const script = document.createElement('script')
    script.innerHTML = `
      (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)}
        h._hjSettings={hjid:${hjid},hjsv:${hjsv}}
        a=o.getElementsByTagName('head')[0]
        r=o.createElement('script');r.async=1
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv
        a.appendChild(r)
      })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=')
    `
    document.head.appendChild(script)

    return () => {
      // Cleanup if needed
      if (script.parentNode) {
        document.head.removeChild(script)
      }
    }
  }, [hjid, hjsv])

  return null
}
