'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import Image, { ImageProps } from 'next/image'
import {
  generateBlurPlaceholder,
  normalizeMediaPath,
  isVideoFile,
} from '@/lib/imageUtils'

type ProgressiveImageProps = Omit<
  ImageProps,
  'src' | 'placeholder' | 'blurDataURL'
> & {
  src: string
  fallbackSrc?: string
  wrapperClassName?: string
  treatGifAsVideo?: boolean
  aspectRatio?: number
  enableIntersectionObserver?: boolean
  rootMargin?: string
  threshold?: number
  onLoadStart?: () => void
  onLoadComplete?: () => void
  onError?: (error: Error) => void
}

/**
 * ProgressiveImage component with advanced loading features
 * - Intersection Observer for lazy loading
 * - Enhanced blur placeholders
 * - Smooth loading transitions
 * - Error handling and fallbacks
 */
export function ProgressiveImage({
  src,
  alt,
  width = 1200,
  height = 675,
  fallbackSrc,
  className,
  wrapperClassName,
  treatGifAsVideo = true,
  aspectRatio,
  enableIntersectionObserver = true,
  rootMargin = '50px',
  threshold = 0.1,
  onLoadStart,
  onLoadComplete,
  onError,
  loading = 'lazy',
  priority = false,
  ...props
}: ProgressiveImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [isInView, setIsInView] = useState(
    !enableIntersectionObserver || priority,
  )
  const [error, setError] = useState<Error | null>(null)
  const [isGif, setIsGif] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)
  const imgRef = useRef<HTMLDivElement>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)

  // Calculate aspect ratio
  const calculatedAspectRatio =
    aspectRatio ||
    (typeof width === 'number' && typeof height === 'number'
      ? width / height
      : 16 / 9)

  // Process the image source
  const getImageSrc = useCallback(() => {
    if (error && fallbackSrc) return normalizeMediaPath(fallbackSrc)
    return normalizeMediaPath(src)
  }, [src, fallbackSrc, error])

  // Check if this is a GIF that should be treated as video
  useEffect(() => {
    const processedSrc = getImageSrc()
    const isGifFile = processedSrc.match(/\.gif($|\?)/i)
    setIsGif(Boolean(isGifFile) && treatGifAsVideo)
  }, [getImageSrc, treatGifAsVideo])

  // Intersection Observer setup
  useEffect(() => {
    if (!enableIntersectionObserver || priority || isInView) return

    const currentRef = imgRef.current
    if (!currentRef) return

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true)
            observerRef.current?.disconnect()
          }
        })
      },
      {
        rootMargin,
        threshold,
      },
    )

    observerRef.current.observe(currentRef)

    return () => {
      observerRef.current?.disconnect()
    }
  }, [enableIntersectionObserver, priority, isInView, rootMargin, threshold])

  // Handle image load events
  const handleLoadStart = useCallback(() => {
    setIsLoading(true)
    onLoadStart?.()
  }, [onLoadStart])

  const handleLoad = useCallback(() => {
    setIsLoading(false)
    setImageLoaded(true)
    onLoadComplete?.()
  }, [onLoadComplete])

  const handleError = useCallback(
    (errorEvent: any) => {
      const errorObj = new Error(`Failed to load image: ${src}`)
      setError(errorObj)
      setIsLoading(false)
      onError?.(errorObj)
    },
    [src, onError],
  )

  // Reset states when src changes
  useEffect(() => {
    setIsLoading(true)
    setError(null)
    setImageLoaded(false)
  }, [src])

  // Generate blur placeholder
  const blurDataURL = generateBlurPlaceholder(calculatedAspectRatio, false)

  // If it's a GIF and we want to treat it as video, render it as a video element
  if (isGif && isInView) {
    return (
      <div
        ref={imgRef}
        className={`relative overflow-hidden ${wrapperClassName || ''}`}
      >
        <video
          src={getImageSrc()}
          autoPlay
          loop
          muted
          playsInline
          className={`w-full h-auto object-contain transition-opacity duration-500 rounded-sm ${
            isLoading ? 'opacity-0' : 'opacity-100'
          } ${className || ''}`}
          width={width}
          height={height}
          onLoadedData={handleLoad}
          onError={handleError}
          onLoadStart={handleLoadStart}
        />
        {isLoading && (
          <div
            className="absolute inset-0 transition-opacity duration-300"
            style={{
              backgroundImage: `url(${blurDataURL})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          />
        )}
      </div>
    )
  }

  // Render placeholder if not in view
  if (!isInView) {
    return (
      <div
        ref={imgRef}
        className={`relative overflow-hidden ${wrapperClassName || ''}`}
        style={{
          minHeight: typeof height === 'number' ? `${height}px` : '200px',
        }}
      >
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url(${blurDataURL})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      </div>
    )
  }

  // Render the actual image
  return (
    <div
      ref={imgRef}
      className={`relative overflow-hidden ${wrapperClassName || ''}`}
    >
      {/* Loading placeholder with blur background */}
      {isLoading && (
        <div
          className="absolute inset-0 transition-opacity duration-300"
          style={{
            backgroundImage: `url(${blurDataURL})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      )}

      {/* Actual image */}
      <Image
        {...props}
        src={getImageSrc()}
        alt={alt}
        width={width}
        height={height}
        quality={90}
        className={`w-full h-auto object-contain transition-opacity duration-500 ease-out rounded-sm ${
          imageLoaded ? 'opacity-100' : 'opacity-0'
        } ${className || ''}`}
        onLoad={handleLoad}
        onError={handleError}
        onLoadStart={handleLoadStart}
        placeholder="blur"
        blurDataURL={blurDataURL}
        loading={priority ? 'eager' : loading}
        priority={priority}
      />

      {/* Error state */}
      {error && !fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <div className="text-sm">Failed to load image</div>
          </div>
        </div>
      )}
    </div>
  )
}
