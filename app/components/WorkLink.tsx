'use client'
import { StaticImageData } from 'next/image'
import { ArrowUpRight } from 'lucide-react'
import { IMAGE_CONTEXTS } from '@/lib/imageUtils'

// Process image URL to ensure it points to the correct location
function getProcessedImageUrl(
  url: string | StaticImageData,
): string | StaticImageData {
  if (typeof url !== 'string') return url

  // If it's already a StaticImageData or starts with /images/, return as is
  if (url.startsWith('/images/')) return url

  // If it starts with / but not with /images/, add /images prefix
  if (url.startsWith('/') && !url.startsWith('/images/')) {
    return `/images${url}`
  }

  // If it doesn't start with /, prepend /images/
  if (!url.startsWith('/')) {
    return `/images/${url}`
  }

  return url
}

// Generate DPI-optimized image with proper srcset (Intercom style)
function generateDPIOptimizedImage(src: string, alt: string): JSX.Element {
  // Get the base path without extension
  const basePath = src.replace(/\.[^.]+$/, '')

  // Use thumbnail context for WorkLink images
  const contextConfig = IMAGE_CONTEXTS.thumbnail
  const contextDPI = contextConfig.dpi

  // Generate DPI-based srcset (Intercom style)
  const srcset = contextDPI
    .map((dpr) => {
      const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
      return `${basePath}${dpiInfo}.avif ${dpr}x`
    })
    .join(', ')

  // Main src uses 1x variant
  const mainSrc = `${basePath}_1x.avif`

  return (
    <img
      src={mainSrc}
      srcSet={srcset}
      alt={alt}
      className="mt-0 mb-0 h-full w-full object-cover object-center transition-all duration-500 ease-out group-hover:opacity-50"
      style={{ width: '100%', height: '100%' }}
      loading="lazy"
      decoding="async"
    />
  )
}

// Component for displaying work link thumbnail
function WorkLinkThumb({
  image,
  title,
}: {
  image: string | StaticImageData
  title: string
}) {
  if (!image) {
    return null
  }

  // Process the URL to ensure it points to the correct location
  const processedUrl = getProcessedImageUrl(image)

  // Handle StaticImageData (imported images)
  if (typeof processedUrl !== 'string') {
    return (
      <div className="group relative aspect-video w-full overflow-hidden rounded-sm">
        <img
          src={processedUrl.src}
          alt={title}
          className="mt-0 mb-0 h-full w-full object-cover object-center transition-all duration-500 ease-out group-hover:opacity-50"
          style={{ width: '100%', height: '100%' }}
          loading="lazy"
          decoding="async"
        />

        {/* Pill in top right corner of image */}
        <div className="absolute top-2 right-2 flex items-center gap-1 rounded-full bg-black/70 px-2 py-1 text-xs text-white backdrop-blur-sm">
          <span>See case study</span>
          <ArrowUpRight className="h-3 w-3" />
        </div>

        {/* Arrow overlay that appears on hover */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-black/70 text-white">
            <ArrowUpRight className="h-6 w-6" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="group relative aspect-video w-full overflow-hidden rounded-sm">
      {generateDPIOptimizedImage(processedUrl, title)}

      {/* Pill in top right corner of image */}
      <div className="absolute top-2 right-2 flex items-center gap-1 rounded-full bg-black/70 px-2 py-1 text-xs text-white backdrop-blur-sm">
        <span>See case study</span>
        <ArrowUpRight className="h-3 w-3" />
      </div>

      {/* Arrow overlay that appears on hover */}
      <div className="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-black/70 text-white">
          <ArrowUpRight className="h-6 w-6" />
        </div>
      </div>
    </div>
  )
}

// Props for the WorkLink component
export type WorkLinkProps = {
  title: string
  subtitle?: string
  image: string | StaticImageData
  url: string
}

// Main WorkLink component that displays a single work link item
export function WorkLink({ title, subtitle, image, url }: WorkLinkProps) {
  // Determine if the link is external
  const isExternalLink = url.startsWith('http') || url.startsWith('https')

  return (
    <a
      href={url}
      target={isExternalLink ? '_blank' : '_self'}
      rel={isExternalLink ? 'noopener noreferrer' : undefined}
      className="block space-y-2 no-underline"
    >
      <div className="relative overflow-hidden rounded-sm bg-zinc-50/40 p-1 ring-1 ring-zinc-200/50 ring-inset dark:bg-zinc-950/40 dark:ring-zinc-800/50">
        <WorkLinkThumb image={image} title={title} />
      </div>
      <div className="px-1">
        <div className="font-base relative inline-flex items-center gap-2 font-[450] text-zinc-900 dark:text-zinc-50">
          {title}
          {isExternalLink && <ArrowUpRight className="h-4 w-4 text-zinc-500" />}
        </div>
        {subtitle && (
          <p className="mt-0 text-base font-light text-zinc-700 dark:text-zinc-200">
            {subtitle}
          </p>
        )}
      </div>
    </a>
  )
}

// Container component for multiple WorkLinks in a grid layout
export function WorkLinksGrid({ children }: { children: React.ReactNode }) {
  return (
    <div className="wide-image my-8">
      <div className="mx-auto grid max-w-[1200px] grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2 md:px-6">
        {children}
      </div>
    </div>
  )
}
