'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import {
  generateBlurPlaceholder,
  normalizeMediaPath,
  IMAGE_CONTEXTS,
} from '@/lib/imageUtils'
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver'

// Video component for MDX content
const MDXVideo = ({
  src,
  alt,
  className,
  width,
  height,
  onLoad,
  onError,
}: {
  src: string
  alt: string
  className?: string
  width: number
  height: number
  onLoad: () => void
  onError: () => void
}) => {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    const video = videoRef.current
    if (video) {
      // Simple autoplay attempt
      const attemptPlay = () => {
        video.play().catch((error) => {
          console.error('MDX Video autoplay failed:', error)
        })
      }

      // Try to play when loaded
      if (video.readyState >= 2) {
        attemptPlay()
      } else {
        video.addEventListener('loadeddata', attemptPlay, { once: true })
      }
    }
  }, [src])

  return (
    <video
      ref={videoRef}
      src={src}
      autoPlay
      loop
      muted
      playsInline
      className={className}
      width={width}
      height={height}
      onLoadedData={onLoad}
      onError={onError}
    >
      <p>{alt || 'Your browser does not support the video tag.'}</p>
    </video>
  )
}

type MDXImageProps = {
  src: string
  alt: string
  className?: string
  caption?: string
  enableProgressiveLoading?: boolean
}

export function MDXImage({
  src,
  alt,
  className = '',
  caption,
  enableProgressiveLoading = true,
}: MDXImageProps) {
  const [aspectRatio, setAspectRatio] = useState(16 / 9)
  const [isVideo, setIsVideo] = useState(false)

  // Check if this is a wide or medium image
  const isWide =
    alt.includes(':wide') || alt.includes('(wide)') || alt.includes('[wide]')
  const isMid =
    alt.includes(':mid') || alt.includes('(mid)') || alt.includes('[mid]')

  // Clean alt text by removing wide/mid modifiers and caption syntax
  const cleanAlt = alt
    .replace(/\s*:wide|\s*\(wide\)|\s*\[wide\]/g, '')
    .replace(/\s*:mid|\s*\(mid\)|\s*\[mid\]/g, '')
    .replace(/\s*:caption=[^:]+/g, '')
    .trim()

  // Use intersection observer for progressive loading
  const { isIntersecting, ref } = useIntersectionObserver({
    threshold: 0.01,
    rootMargin: '200px',
    triggerOnce: true,
    skip: !enableProgressiveLoading,
  })

  // Process the image source
  const getImageSrc = useCallback(() => {
    return normalizeMediaPath(src)
  }, [src])

  useEffect(() => {
    if (!src || !isIntersecting) return

    // Check if this is a video file
    const videoMatch = getImageSrc().match(/\.(mp4|webm|ogg|mov)($|\?)/i)
    if (videoMatch) {
      setIsVideo(true)
      return
    }

    // Handle image files - only get aspect ratio
    const img = new window.Image()
    img.onload = () => {
      setAspectRatio(img.width / img.height)
    }
    img.src = getImageSrc()
  }, [src, isIntersecting, getImageSrc])

  // Detect image context for DPI optimization
  const imageContext = isWide ? 'wide' : isMid ? 'mid' : 'regular'
  const contextConfig = IMAGE_CONTEXTS[imageContext]

  // Generate DPI-optimized srcset (Intercom style)
  const generateDPISrcSet = (src: string) => {
    const basePath = src.replace(/\.[^.]+$/, '')
    return contextConfig.dpi
      .map((dpr) => {
        const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
        return `${basePath}${dpiInfo}.avif ${dpr}x`
      })
      .join(', ')
  }

  // Generate main src (1x variant)
  const getMainSrc = (src: string) => {
    const basePath = src.replace(/\.[^.]+$/, '')
    return `${basePath}_1x.avif`
  }

  // Generate sizes attribute based on context
  const getSizesAttribute = () => {
    switch (imageContext) {
      case 'wide':
        return '100vw'
      case 'mid':
        return '(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 1200px'
      default:
        return '(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 800px'
    }
  }

  // Determine if content should be loaded
  const shouldLoad = !enableProgressiveLoading || isIntersecting

  // Generate container classes for wide, medium, vs regular images
  // Use the same wide-image and mid-image classes as MDXContent.tsx for consistency
  // Apply margin to the entire figure (image + caption) instead of just the image
  const containerClasses = isWide
    ? `wide-image my-8 overflow-hidden ${className}` // Use wide-image class with 24px margins from CSS
    : isMid
      ? `mid-image my-8 overflow-hidden ${className}` // Use mid-image class for medium sizing
      : `my-8 w-full overflow-hidden ${className}`

  const figureStyle = { aspectRatio: `${aspectRatio}` }

  // Render placeholder if not ready to load
  if (!shouldLoad) {
    return (
      <figure className={containerClasses} style={figureStyle}>
        <div
          ref={ref}
          className="w-full h-full rounded-sm"
          style={{
            minHeight: '250px',
            backgroundColor: '#f3f4f6',
          }}
        />
        {caption && (
          <p className="mt-0.5 text-base leading-relaxed text-zinc-700 dark:text-zinc-200">
            {caption}
          </p>
        )}
      </figure>
    )
  }

  return (
    <figure className={containerClasses}>
      {isVideo ? (
        <div ref={ref}>
          <MDXVideo
            src={getImageSrc()}
            alt={cleanAlt}
            className="w-full h-auto object-contain transition-all duration-500 ease-out rounded-sm"
            width={contextConfig.baseWidth}
            height={Math.round(contextConfig.baseWidth / aspectRatio)}
            onLoad={() => {}}
            onError={() => {}}
          />
        </div>
      ) : (
        <img
          ref={ref}
          src={getMainSrc(getImageSrc())}
          srcSet={generateDPISrcSet(getImageSrc())}
          alt={cleanAlt || 'Image'}
          sizes={getSizesAttribute()}
          className="w-full h-auto object-contain transition-all duration-500 ease-out rounded-sm"
          loading="lazy" width={contextConfig.baseWidth} height={Math.round(contextConfig.baseWidth / aspectRatio)}
          decoding="async"
        />
      )}
      {caption && (
        <p className="mt-4 text-base leading-relaxed text-zinc-700 dark:text-zinc-200">
          {caption}
        </p>
      )}
    </figure>
  )
}
