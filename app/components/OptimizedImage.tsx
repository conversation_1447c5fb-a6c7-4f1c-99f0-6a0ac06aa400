'use client'

import { useState, useEffect, useCallback } from 'react'
import Image, { ImageProps } from 'next/image'
import { generateBlurPlaceholder, normalizeMediaPath } from '@/lib/imageUtils'
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver'

type OptimizedImageProps = Omit<
  ImageProps,
  'src' | 'placeholder' | 'blurDataURL'
> & {
  src: string
  fallbackSrc?: string
  wrapperClassName?: string
  treatGifAsVideo?: boolean
  aspectRatio?: number
  enableProgressiveLoading?: boolean
  rootMargin?: string
}

/**
 * OptimizedImage component for Next.js static exports
 * This component ensures images are properly optimized in static exports
 */
export function OptimizedImage({
  src,
  alt,
  width = 1200,
  height = 675,
  fallbackSrc,
  className,
  wrapperClassName,
  treatGifAsVideo = true,
  aspectRatio,
  enableProgressiveLoading = true,
  rootMargin = '100px',
  loading = 'lazy',
  priority = false,
  ...props
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(false)
  const [isGif, setIsGif] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  // Calculate aspect ratio
  const calculatedAspectRatio =
    aspectRatio ||
    (typeof width === 'number' && typeof height === 'number'
      ? width / height
      : 16 / 9)

  // Use intersection observer for progressive loading
  const { isIntersecting, ref } = useIntersectionObserver({
    threshold: 0.1,
    rootMargin,
    triggerOnce: true,
    skip: !enableProgressiveLoading || priority,
  })

  // Process the image source
  const getImageSrc = useCallback(() => {
    if (error && fallbackSrc) return normalizeMediaPath(fallbackSrc)
    return normalizeMediaPath(src)
  }, [src, fallbackSrc, error])

  // Check if this is a GIF that should be treated as video
  useEffect(() => {
    const processedSrc = getImageSrc()
    const isGifFile = processedSrc.match(/\.gif($|\?)/i)
    setIsGif(Boolean(isGifFile) && treatGifAsVideo)
  }, [src, treatGifAsVideo])

  // Handle image load events
  const handleLoad = useCallback(() => {
    setIsLoading(false)
    setImageLoaded(true)
  }, [])

  const handleError = useCallback(() => {
    setError(true)
    setIsLoading(false)
  }, [])

  // Reset loading state when src changes
  useEffect(() => {
    setIsLoading(true)
    setError(false)
    setImageLoaded(false)
  }, [src])

  // Generate blur placeholder
  const blurDataURL = generateBlurPlaceholder(calculatedAspectRatio, false)

  // Determine if image should be loaded
  const shouldLoad = !enableProgressiveLoading || priority || isIntersecting

  // If it's a GIF and we want to treat it as video, render it as a video element
  if (isGif && shouldLoad) {
    return (
      <div
        ref={ref}
        className={`relative overflow-hidden ${wrapperClassName || ''}`}
      >
        <video
          src={getImageSrc()}
          autoPlay
          loop
          muted
          playsInline
          className={`w-full h-auto object-contain transition-opacity duration-500 rounded-sm ${
            isLoading ? 'opacity-0' : 'opacity-100'
          } ${className || ''}`}
          width={width}
          height={height}
          onLoadedData={handleLoad}
          onError={handleError}
        />
        {isLoading && (
          <div
            className="absolute inset-0 transition-opacity duration-300"
            style={{
              backgroundImage: `url(${blurDataURL})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          />
        )}
      </div>
    )
  }

  // Render placeholder if not ready to load
  if (!shouldLoad) {
    return (
      <div
        ref={ref}
        className={`relative overflow-hidden ${wrapperClassName || ''}`}
        style={{
          minHeight: typeof height === 'number' ? `${height}px` : '200px',
        }}
      >
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url(${blurDataURL})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      </div>
    )
  }

  // Otherwise render as normal image
  return (
    <div
      ref={ref}
      className={`relative overflow-hidden ${wrapperClassName || ''}`}
    >
      {/* Loading placeholder with blur background */}
      {isLoading && (
        <div
          className="absolute inset-0 transition-opacity duration-300"
          style={{
            backgroundImage: `url(${blurDataURL})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      )}

      {/* Actual image */}
      <Image
        {...props}
        src={getImageSrc()}
        alt={alt}
        width={width}
        height={height}
        quality={90}
        className={`w-full h-auto object-contain transition-opacity duration-500 ease-out rounded-sm ${
          imageLoaded ? 'opacity-100' : 'opacity-0'
        } ${className || ''}`}
        onLoad={handleLoad}
        onError={handleError}
        placeholder="blur"
        blurDataURL={blurDataURL}
        loading={priority ? 'eager' : loading}
        priority={priority}
      />

      {/* Error state */}
      {error && !fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <div className="text-sm">Failed to load image</div>
          </div>
        </div>
      )}
    </div>
  )
}
