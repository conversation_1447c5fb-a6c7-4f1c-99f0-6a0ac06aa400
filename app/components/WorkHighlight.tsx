'use client'

import React from 'react'
import parse from 'html-react-parser'

type WorkHighlightProps = {
  content: string
}

export function WorkHighlight({ content }: WorkHighlightProps) {
  return (
    <div className="my-12">
      <div className="relative rounded-lg bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800/50 p-6 md:p-8">
        {/* Icon */}
        <div className="mb-4">
          <div className="inline-flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/50">
            <svg
              className="h-5 w-5 text-blue-600 dark:text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        </div>

        {/* Content */}
        <div className="text-base leading-relaxed text-zinc-800 dark:text-zinc-200">
          {parse(content)}
        </div>
      </div>
    </div>
  )
}
