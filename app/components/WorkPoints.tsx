'use client'

import React from 'react'
import parse from 'html-react-parser'

type PointItem = {
  title: string
  subtitle: string | React.ReactNode
}

type WorkPointsProps = {
  items: PointItem[]
}

type WorkPointsThreeColProps = {
  items: PointItem[]
}

type WorkPointsMidProps = {
  items: PointItem[]
}

type WorkPointsThreeColMidProps = {
  items: PointItem[]
}

// Original 2-column WorkPoints component
export function WorkPoints({ items }: WorkPointsProps) {
  return (
    <div className="my-12">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 md:gap-8">
        {items.map((item, index) => (
          <div key={index} className="flex flex-col space-y-2">
            <h3 className="mt-0 mb-2 text-lg font-medium text-zinc-900 dark:text-zinc-50">
              {item.title}
            </h3>
            <div className="text-base text-zinc-700 dark:text-zinc-200">
              {typeof item.subtitle === 'string' ? (
                <div className="mt-0 mb-0">
                  {parse(item.subtitle as string)}
                </div>
              ) : (
                item.subtitle
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// 3-column variant
export function WorkPointsThreeCol({ items }: WorkPointsThreeColProps) {
  return (
    <div className="wide-image my-12">
      <div className="mx-auto max-w-[1200px] md:px-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 md:gap-8 lg:grid-cols-3">
          {items.map((item, index) => (
            <div key={index} className="flex flex-col space-y-2">
              <h3 className="mt-0 mb-2 text-lg font-medium text-zinc-900 dark:text-zinc-50">
                {item.title}
              </h3>
              <div className="text-base text-zinc-700 dark:text-zinc-200">
                {typeof item.subtitle === 'string' ? (
                  <div className="mt-0 mb-0">
                    {parse(item.subtitle as string)}
                  </div>
                ) : (
                  item.subtitle
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// 2-column variant with (mid) max-width 1200px
export function WorkPointsMid({ items }: WorkPointsMidProps) {
  return (
    <div className="wide-image my-12">
      <div className="mx-auto max-w-[1200px] md:px-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 md:gap-8">
          {items.map((item, index) => (
            <div key={index} className="flex flex-col space-y-2">
              <h3 className="mt-0 mb-2 text-lg font-medium text-zinc-900 dark:text-zinc-50">
                {item.title}
              </h3>
              <div className="text-base text-zinc-700 dark:text-zinc-200">
                {typeof item.subtitle === 'string' ? (
                  <div className="mt-0 mb-0">
                    {parse(item.subtitle as string)}
                  </div>
                ) : (
                  item.subtitle
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// 3-column variant with (mid) max-width 1200px
export function WorkPointsThreeColMid({ items }: WorkPointsThreeColMidProps) {
  return (
    <div className="wide-image my-12">
      <div className="mx-auto max-w-[1200px] md:px-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 md:gap-8 lg:grid-cols-3">
          {items.map((item, index) => (
            <div key={index} className="flex flex-col space-y-2">
              <h3 className="mt-0 mb-2 text-lg font-medium text-zinc-900 dark:text-zinc-50">
                {item.title}
              </h3>
              <div className="text-base text-zinc-700 dark:text-zinc-200">
                {typeof item.subtitle === 'string' ? (
                  <div className="mt-0 mb-0">
                    {parse(item.subtitle as string)}
                  </div>
                ) : (
                  item.subtitle
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
