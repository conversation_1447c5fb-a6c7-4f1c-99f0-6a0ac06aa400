'use client'

import { useState, useEffect } from 'react'
import Image, { ImageProps } from 'next/image'
import {
  generateBlurPlaceholder,
  normalizeMediaPath,
  isVideoFile,
  detectImageContext,
  generateSizesAttribute,
} from '@/lib/imageUtils'
import {
  generateEnhancedDensitySrcSet,
  getOptimalDPR,
  preloadHiDPIImage,
} from '@/lib/hiDPIUtils'

type ResponsiveImageProps = Omit<
  ImageProps,
  'src' | 'placeholder' | 'blurDataURL'
> & {
  src: string
  context?: 'thumbnail' | 'regular' | 'mid' | 'wide' | 'sideBySide' | 'carousel'
  fallbackSrc?: string
  wrapperClassName?: string
  aspectRatio?: number
  enableProgressiveLoading?: boolean
  autoDetectContext?: boolean
}

/**
 * ResponsiveImage component with Next.js best practices for context-aware responsive images
 * - Uses Next.js built-in srcset generation with custom loader
 * - Automatically detects image context for optimal responsive behavior
 * - Uses pre-generated static images in production
 * - Falls back to query parameters in development
 * - Maintains progressive loading and blur placeholders
 */
export function ResponsiveImage({
  src,
  alt,
  width = 1200,
  height = 675,
  context,
  fallbackSrc,
  className,
  wrapperClassName,
  aspectRatio,
  enableProgressiveLoading = true,
  autoDetectContext = true,
  loading = 'lazy',
  priority = false,
  quality = 90,
  ...props
}: ResponsiveImageProps) {
  const [isLoading, setIsLoading] = useState(enableProgressiveLoading)
  const [error, setError] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  // Calculate aspect ratio
  const calculatedAspectRatio =
    aspectRatio ||
    (typeof width === 'number' && typeof height === 'number'
      ? width / height
      : 16 / 9)

  // Generate blur placeholder
  const blurDataURL = generateBlurPlaceholder(calculatedAspectRatio)

  // Detect or use provided context
  const detectedContext = autoDetectContext
    ? detectImageContext(src, typeof width === 'number' ? width : 1200)
    : context || 'regular'

  // Generate the main image source
  const getImageSrc = () => {
    if (error && fallbackSrc) {
      return normalizeMediaPath(fallbackSrc)
    }
    return normalizeMediaPath(src)
  }

  // Generate sizes attribute based on context
  const generateSizes = () => {
    if (props.sizes) return props.sizes
    return generateSizesAttribute(detectedContext)
  }

  // Generate hi-DPI aware srcset for better image quality on high-density screens
  const generateHiDPISrcSet = () => {
    if (typeof width === 'number') {
      return generateEnhancedDensitySrcSet(getImageSrc(), detectedContext, width)
    }
    return undefined
  }

  // Handle image load
  const handleLoad = () => {
    setIsLoading(false)
    setImageLoaded(true)
    setError(false)
  }

  // Handle image error
  const handleError = () => {
    setError(true)
    setIsLoading(false)
    if (fallbackSrc) {
      // Try loading the fallback
      setImageLoaded(true)
    }
  }

  // Preload critical hi-DPI images
  useEffect(() => {
    if (priority && !isVideoFile(src)) {
      preloadHiDPIImage(getImageSrc(), detectedContext, true)
    }
  }, [priority, src, detectedContext])

  // If it's a video file, don't render as image
  if (isVideoFile(src)) {
    return (
      <div
        className={`relative overflow-hidden ${wrapperClassName || ''}`}
      >
        <video
          src={getImageSrc()}
          autoPlay
          loop
          muted
          playsInline
          className={`w-full h-auto object-contain ${className || ''}`}
          width={width}
          height={height}
        />
      </div>
    )
  }

  return (
    <div
      className={`relative overflow-hidden ${wrapperClassName || ''}`}
    >
      {/* Progressive loading background */}
      {enableProgressiveLoading && isLoading && (
        <div
          className="absolute inset-0 transition-opacity duration-300"
          style={{
            backgroundImage: `url(${blurDataURL})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      )}

      {/* Actual image - Next.js will automatically generate srcset using our custom loader */}
      <Image
        {...props}
        src={getImageSrc()}
        alt={alt}
        width={width}
        height={height}
        sizes={generateSizes()}
        quality={quality}
        className={`w-full h-auto object-contain transition-opacity duration-500 ease-out rounded-sm ${
          imageLoaded ? 'opacity-100' : 'opacity-0'
        } ${className || ''}`}
        onLoad={handleLoad}
        onError={handleError}
        placeholder="blur"
        blurDataURL={blurDataURL}
        loading={priority ? 'eager' : loading}
        priority={priority}
      />

      {/* Error state */}
      {error && !fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <div className="text-sm">Failed to load image</div>
          </div>
        </div>
      )}
    </div>
  )
}
