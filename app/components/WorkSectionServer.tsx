import { WorkSection } from './WorkSection'
import { WorkItem } from '../data/works'
import staticWorksData from '@/app/data/works-data.json'
import fs from 'node:fs'
import path from 'node:path'
import matter from 'gray-matter'

// Helper to determine if we're in development mode
const isDev = process.env.NODE_ENV === 'development'

/**
 * Get work items from markdown files for real-time updates during development
 */
function getWorksFromMarkdown(): WorkItem[] {
  try {
    const workDirectory = path.join(process.cwd(), '_work')

    if (!fs.existsSync(workDirectory)) {
      console.warn('Work directory not found:', workDirectory)
      return []
    }

    // Get all markdown files
    const fileNames = fs
      .readdirSync(workDirectory)
      .filter((fileName) => fileName.endsWith('.md'))

    // Read and parse all work items
    const allWorks = fileNames.map((fileName) => {
      // Remove the .md extension to get the slug
      const slug = fileName.replace(/\.md$/, '')

      // Get the full path to the markdown file
      const fullPath = path.join(workDirectory, fileName)

      // Read the file content
      const fileContents = fs.readFileSync(fullPath, 'utf8')

      // Parse the frontmatter
      const { data } = matter(fileContents)

      // Create the work item
      return {
        slug,
        title: data.title || slug,
        subtitle: data.subtitle || undefined,
        coverImage: data.coverImage || '',
        date: data.date
          ? String(data.date)
          : new Date().toISOString().split('T')[0],
        showOnHomepage:
          data.showOnHomepage !== undefined
            ? Boolean(data.showOnHomepage)
            : true,
      }
    })

    // Sort by date (newest first)
    return allWorks.sort((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime()
    })
  } catch (error) {
    console.error('Error loading works:', error)
    return []
  }
}

/**
 * Server component that loads work data at build time or in real-time during development
 * and passes it to the client component
 */
export function WorkSectionServer() {
  // In development, read directly from markdown files for real-time updates
  const works = isDev ? getWorksFromMarkdown() : (staticWorksData as WorkItem[])

  return <WorkSection works={works} />
}
