'use client'

import React, { useState, useEffect } from 'react'
import { MDXContent } from './MDXContent'
import { decryptContent, sessionManager } from '@/app/lib/simple-crypto'

interface PasswordProtectionWrapperProps {
  password: string
  content: string
  encryptedContent?: string
  slug?: string
}

export default function PasswordProtectionWrapper({
  password,
  content,
  encryptedContent,
  slug,
}: PasswordProtectionWrapperProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [inputPassword, setInputPassword] = useState('')
  const [error, setError] = useState('')
  const [decryptedContent, setDecryptedContent] = useState('')

  // Check if already authenticated
  useEffect(() => {
    if (slug && sessionManager.isAuthenticated(slug)) {
      setIsAuthenticated(true)
      // If we have encrypted content, decrypt it
      if (encryptedContent) {
        try {
          const decrypted = decryptContent(encryptedContent, password)
          setDecryptedContent(decrypted)
        } catch (error) {
          console.error('Failed to decrypt content:', error)
          // Fall back to regular content if decryption fails
          setDecryptedContent(content)
        }
      } else {
        setDecryptedContent(content)
      }
    }
  }, [slug, password, content, encryptedContent])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (inputPassword === password) {
      // Save authentication state
      if (slug) {
        sessionManager.setAuthenticated(slug, password)
      }
      setIsAuthenticated(true)
      setError('')

      // Decrypt content if available
      if (encryptedContent) {
        try {
          const decrypted = decryptContent(encryptedContent, inputPassword)
          setDecryptedContent(decrypted)
        } catch (error) {
          console.error('Failed to decrypt content:', error)
          setDecryptedContent(content)
        }
      } else {
        setDecryptedContent(content)
      }
    } else {
      setError('Incorrect password. Please try again.')
    }
  }

  if (isAuthenticated) {
    return (
      <MDXContent
        key={`${slug || 'protected'}-${decryptedContent.length}`}
        content={decryptedContent || content}
      />
    )
  }

  return (
    <div className="flex min-h-[50vh] flex-col items-center justify-center px-4 py-12">
      <div className="w-full max-w-md space-y-8 rounded-lg bg-white p-8 shadow-md dark:bg-zinc-900">
        <div>
          <h2 className="text-center text-2xl font-bold text-gray-900 dark:text-white">
            Password Protected Content
          </h2>
          <p className="mt-2 text-center text-sm text-zinc-700 dark:text-zinc-200">
            Please enter the password to view this content
          </p>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="password" className="sr-only">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              required
              className="relative block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm dark:border-zinc-700 dark:bg-zinc-800 dark:text-white dark:placeholder-zinc-400"
              placeholder="Password"
              value={inputPassword}
              onChange={(e) => setInputPassword(e.target.value)}
            />
          </div>

          {error && (
            <div className="text-center text-sm text-red-500">{error}</div>
          )}

          <div>
            <button
              type="submit"
              className="flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none dark:ring-offset-zinc-900"
            >
              Enter
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
