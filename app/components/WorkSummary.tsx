'use client'

import React from 'react'
import parse from 'html-react-parser'

type SummaryItem = {
  label: string
  value: string | React.ReactNode
}

type WorkSummaryProps = {
  items: SummaryItem[]
}

export function WorkSummary({ items }: WorkSummaryProps) {
  return (
    <div className="mt-12">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3 md:gap-8">
        {items.map((item, index) => (
          <div key={index} className="flex flex-col">
            <h3 className="mt-0 mb-1 text-lg font-medium">{item.label}</h3>
            <div className="text-base text-zinc-700 dark:text-zinc-200">
              {typeof item.value === 'string' ? (
                <div className="mt-0 mb-0">{parse(item.value as string)}</div>
              ) : (
                item.value
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
