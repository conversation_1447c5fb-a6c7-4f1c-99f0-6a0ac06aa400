'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import Image, { ImageProps } from 'next/image'
import {
  generateBlurPlaceholder,
  normalizeMediaPath,
  isVideoFile,
} from '@/lib/imageUtils'
import { getCachedLQIP, generateGradientLQIP } from '@/lib/lqipGenerator'
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver'

type SmartProgressiveImageProps = Omit<
  ImageProps,
  'src' | 'placeholder' | 'blurDataURL'
> & {
  src: string
  fallbackSrc?: string
  wrapperClassName?: string
  treatGifAsVideo?: boolean
  aspectRatio?: number
  enableIntersectionObserver?: boolean
  enableSmartLQIP?: boolean
  rootMargin?: string
  threshold?: number
  onLoadStart?: () => void
  onLoadComplete?: () => void
  onError?: (error: Error) => void
  preloadColors?: string[]
}

/**
 * SmartProgressiveImage component with intelligent LQIP generation
 * - Generates actual low-quality versions of images when possible
 * - Falls back to color-based gradients
 * - Intersection Observer for lazy loading
 * - Enhanced blur placeholders
 * - Smooth loading transitions
 */
export function SmartProgressiveImage({
  src,
  alt,
  width = 1200,
  height = 675,
  fallbackSrc,
  className,
  wrapperClassName,
  treatGifAsVideo = true,
  aspectRatio,
  enableIntersectionObserver = true,
  enableSmartLQIP = true,
  rootMargin = '50px',
  threshold = 0.1,
  onLoadStart,
  onLoadComplete,
  onError,
  loading = 'lazy',
  priority = false,
  preloadColors,
  ...props
}: SmartProgressiveImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [isInView, setIsInView] = useState(
    !enableIntersectionObserver || priority,
  )
  const [error, setError] = useState<Error | null>(null)
  const [isGif, setIsGif] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)
  const [smartBlurDataURL, setSmartBlurDataURL] = useState<string>('')
  const [isDarkMode, setIsDarkMode] = useState(false)
  const imgRef = useRef<HTMLDivElement>(null)

  // Calculate aspect ratio
  const calculatedAspectRatio =
    aspectRatio ||
    (typeof width === 'number' && typeof height === 'number'
      ? width / height
      : 16 / 9)

  // Detect dark mode
  useEffect(() => {
    const checkDarkMode = () => {
      setIsDarkMode(document.documentElement.classList.contains('dark'))
    }

    checkDarkMode()

    // Watch for dark mode changes
    const observer = new MutationObserver(checkDarkMode)
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class'],
    })

    return () => observer.disconnect()
  }, [])

  // Process the image source
  const getImageSrc = useCallback(() => {
    if (error && fallbackSrc) return normalizeMediaPath(fallbackSrc)
    return normalizeMediaPath(src)
  }, [src, fallbackSrc, error])

  // Use intersection observer for progressive loading
  const { isIntersecting, ref } = useIntersectionObserver({
    threshold,
    rootMargin,
    triggerOnce: true,
    skip: !enableIntersectionObserver || priority,
  })

  // Update isInView when intersection changes
  useEffect(() => {
    if (isIntersecting) {
      setIsInView(true)
    }
  }, [isIntersecting])

  // Check if this is a GIF that should be treated as video
  useEffect(() => {
    const processedSrc = getImageSrc()
    const isGifFile = processedSrc.match(/\.gif($|\?)/i)
    setIsGif(Boolean(isGifFile) && treatGifAsVideo)
  }, [getImageSrc, treatGifAsVideo])

  // Generate smart LQIP when component mounts or src changes
  useEffect(() => {
    if (!enableSmartLQIP) {
      setSmartBlurDataURL(
        generateBlurPlaceholder(calculatedAspectRatio, isDarkMode),
      )
      return
    }

    const generateLQIP = async () => {
      try {
        if (preloadColors && preloadColors.length > 0) {
          // Use provided colors for gradient
          const gradientLQIP = generateGradientLQIP(
            preloadColors,
            calculatedAspectRatio,
            isDarkMode,
          )
          setSmartBlurDataURL(gradientLQIP)
        } else {
          // Generate smart LQIP
          const lqip = await getCachedLQIP(
            getImageSrc(),
            calculatedAspectRatio,
            isDarkMode,
          )
          setSmartBlurDataURL(lqip)
        }
      } catch (error) {
        console.warn('Smart LQIP generation failed, using fallback:', error)
        setSmartBlurDataURL(
          generateBlurPlaceholder(calculatedAspectRatio, isDarkMode),
        )
      }
    }

    generateLQIP()
  }, [
    src,
    calculatedAspectRatio,
    isDarkMode,
    enableSmartLQIP,
    preloadColors,
    getImageSrc,
  ])

  // Handle image load events
  const handleLoadStart = useCallback(() => {
    setIsLoading(true)
    onLoadStart?.()
  }, [onLoadStart])

  const handleLoad = useCallback(() => {
    setIsLoading(false)
    setImageLoaded(true)
    onLoadComplete?.()
  }, [onLoadComplete])

  const handleError = useCallback(
    (errorEvent: any) => {
      const errorObj = new Error(`Failed to load image: ${src}`)
      setError(errorObj)
      setIsLoading(false)
      onError?.(errorObj)
    },
    [src, onError],
  )

  // Reset states when src changes
  useEffect(() => {
    setIsLoading(true)
    setError(null)
    setImageLoaded(false)
  }, [src])

  // Combine refs
  const combinedRef = useCallback(
    (element: HTMLDivElement | null) => {
      imgRef.current = element
      ref(element)
    },
    [ref],
  )

  // If it's a GIF and we want to treat it as video, render it as a video element
  if (isGif && isInView) {
    return (
      <div
        ref={combinedRef}
        className={`relative overflow-hidden ${wrapperClassName || ''}`}
      >
        <video
          src={getImageSrc()}
          autoPlay
          loop
          muted
          playsInline
          className={`w-full h-auto object-contain transition-opacity duration-500 rounded-sm ${
            isLoading ? 'opacity-0' : 'opacity-100'
          } ${className || ''}`}
          width={width}
          height={height}
          onLoadedData={handleLoad}
          onError={handleError}
          onLoadStart={handleLoadStart}
        />
        {isLoading && smartBlurDataURL && (
          <div
            className="absolute inset-0 transition-opacity duration-300"
            style={{
              backgroundImage: `url(${smartBlurDataURL})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          />
        )}
      </div>
    )
  }

  // Render placeholder if not in view
  if (!isInView) {
    return (
      <div
        ref={combinedRef}
        className={`relative overflow-hidden ${wrapperClassName || ''}`}
        style={{
          minHeight: typeof height === 'number' ? `${height}px` : '200px',
          backgroundImage: smartBlurDataURL
            ? `url(${smartBlurDataURL})`
            : undefined,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        <div
          className="absolute inset-0 opacity-50"
          style={{
            backgroundImage: smartBlurDataURL
              ? `url(${smartBlurDataURL})`
              : undefined,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      </div>
    )
  }

  // Render the actual image
  return (
    <div
      ref={combinedRef}
      className={`relative overflow-hidden ${wrapperClassName || ''}`}
    >
      {/* Smart LQIP background */}
      {smartBlurDataURL && (
        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            imageLoaded ? 'opacity-0' : 'opacity-100'
          }`}
          style={{
            backgroundImage: `url(${smartBlurDataURL})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      )}

      {/* Loading overlay - only show if no smart blur background */}
      {isLoading && !smartBlurDataURL && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-100/50 to-gray-200/50 transition-opacity duration-300 dark:from-gray-800/50 dark:to-gray-900/50" />
      )}

      {/* Actual image */}
      <Image
        {...props}
        src={getImageSrc()}
        alt={alt}
        width={width}
        height={height}
        quality={90}
        className={`w-full h-auto object-contain transition-opacity duration-700 ease-out rounded-sm ${
          imageLoaded ? 'opacity-100' : 'opacity-0'
        } ${className || ''}`}
        onLoad={handleLoad}
        onError={handleError}
        onLoadStart={handleLoadStart}
        placeholder="blur"
        blurDataURL={
          smartBlurDataURL ||
          generateBlurPlaceholder(calculatedAspectRatio, isDarkMode)
        }
        loading={priority ? 'eager' : loading}
        priority={priority}
      />

      {/* Error state */}
      {error && !fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <div className="text-sm">Failed to load image</div>
          </div>
        </div>
      )}
    </div>
  )
}
