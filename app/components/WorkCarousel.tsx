'use client'
import React, { useState, useCallback, useRef, useEffect } from 'react'
import Image from 'next/image'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'

// Video component for carousel items
const CarouselVideo = ({
  src,
  alt,
  className,
  style,
}: {
  src: string
  alt: string
  className?: string
  style?: React.CSSProperties
}) => {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    const video = videoRef.current
    if (video) {
      console.log('Video component mounted with src:', src)

      // Simple autoplay attempt
      const attemptPlay = () => {
        video.play().catch((error) => {
          console.error('Video autoplay failed:', error, src)
        })
      }

      // Try to play when loaded
      if (video.readyState >= 2) {
        attemptPlay()
      } else {
        video.addEventListener('loadeddata', attemptPlay, { once: true })
      }
    }
  }, [src])

  return (
    <video
      ref={videoRef}
      src={src}
      autoPlay
      loop
      muted
      playsInline
      className={className}
      style={style}
      width={800}
      height={600}
      onLoadedData={() => console.log('Video loaded:', src)}
      onError={(e) => console.error('Video error:', e, src)}
      onCanPlay={() => console.log('Video can play:', src)}
      onPlay={() => console.log('Video is playing:', src)}
    >
      <p>{alt || 'Your browser does not support the video tag.'}</p>
    </video>
  )
}

interface CarouselImage {
  src: string
  alt: string
  caption?: string
}

interface WorkCarouselProps {
  images: CarouselImage[]
  className?: string
}

export function WorkCarousel({ images, className }: WorkCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const totalSlides = images.length

  // Check if we're on mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Calculate slides per view
  // Show 2.2 images at a time (2 full + partial third) on desktop
  // Show 1.2 images on mobile (1 full + partial second)
  const slidesPerView = isMobile ? 1.2 : 2.2

  // Touch handling constants
  const minSwipeDistance = 50

  // Touch event handlers
  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null) // Reset touchEnd
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isLeftSwipe) {
      goToNext()
    } else if (isRightSwipe) {
      goToPrev()
    }
  }

  // Process image URL to ensure it points to the correct location
  const processImageUrl = (url: string): string => {
    // If it's an external URL, return as is
    if (url.startsWith('http')) {
      return url
    }

    // If src starts with / but not with /images/, add /images prefix
    if (url.startsWith('/') && !url.startsWith('/images/')) {
      return `/images${url}`
    }

    // If src doesn't start with /, prepend /images/
    if (!url.startsWith('/')) {
      return `/images/${url}`
    }

    // Otherwise use the path as is
    return url
  }

  // Check if a file is a video based on its extension
  const isVideoFile = (url: string): boolean => {
    return /\.(mp4|webm|ogg|mov)($|\?)/i.test(url)
  }

  const goToNext = useCallback(() => {
    // Calculate max index - we can scroll until we show the last image
    const maxIndex = Math.max(0, totalSlides - Math.floor(slidesPerView))
    if (isTransitioning || currentIndex >= maxIndex) return

    setIsTransitioning(true)
    setCurrentIndex((prevIndex) => prevIndex + 1)

    setTimeout(() => {
      setIsTransitioning(false)
    }, 300)
  }, [currentIndex, totalSlides, isTransitioning, slidesPerView])

  const goToPrev = useCallback(() => {
    if (isTransitioning || currentIndex <= 0) return

    setIsTransitioning(true)
    setCurrentIndex((prevIndex) => prevIndex - 1)

    setTimeout(() => {
      setIsTransitioning(false)
    }, 300)
  }, [currentIndex, isTransitioning])

  // Calculate the transform for the carousel
  // Each slide takes up (100 / totalSlides)% of the container width
  // We move by exactly one slide width per navigation
  const slideWidth = 100 / totalSlides
  const translateX = -(currentIndex * slideWidth)

  return (
    <div className={cn('wide-image my-8', className)}>
      <div className="mx-auto max-w-[1620px] px-4 md:px-6">
        <div className="space-y-4">
          {/* Controls above the carousel */}
          <div className="flex items-center justify-between">
            {/* Slide counter */}
            <div className="flex items-center text-sm text-zinc-700 dark:text-zinc-200">
              <span>{currentIndex + 1}</span>
              <span className="mx-1">/</span>
              <span>{totalSlides}</span>
            </div>

            {/* Navigation arrows */}
            <div className="flex gap-1">
              <button
                onClick={goToPrev}
                disabled={currentIndex <= 0}
                aria-label="Previous slide"
                className={cn(
                  'flex h-10 w-10 items-center justify-center rounded-full border transition-colors',
                  'border-gray-200 bg-white dark:border-zinc-700 dark:bg-zinc-900',
                  'hover:bg-gray-50 dark:hover:bg-zinc-800',
                  currentIndex <= 0
                    ? 'cursor-not-allowed text-gray-400'
                    : 'text-gray-800 dark:text-gray-200',
                )}
              >
                <ChevronLeft size={20} />
              </button>
              <button
                onClick={goToNext}
                disabled={
                  currentIndex >=
                  Math.max(0, totalSlides - Math.floor(slidesPerView))
                }
                aria-label="Next slide"
                className={cn(
                  'flex h-10 w-10 items-center justify-center rounded-full border transition-colors',
                  'border-gray-200 bg-white dark:border-zinc-700 dark:bg-zinc-900',
                  'hover:bg-gray-50 dark:hover:bg-zinc-800',
                  currentIndex >=
                    Math.max(0, totalSlides - Math.floor(slidesPerView))
                    ? 'cursor-not-allowed text-gray-400'
                    : 'text-gray-800 dark:text-gray-200',
                )}
              >
                <ChevronRight size={20} />
              </button>
            </div>
          </div>

          {/* Carousel container */}
          <div
            className="relative overflow-hidden rounded-lg"
            onTouchStart={onTouchStart}
            onTouchMove={onTouchMove}
            onTouchEnd={onTouchEnd}
          >
            <div
              ref={containerRef}
              className="flex transition-transform duration-300 ease-out"
              style={{
                transform: `translateX(${translateX}%)`,
                width: `${totalSlides * (100 / slidesPerView)}%`,
              }}
            >
              {images.map((image, index) => {
                const processedSrc = processImageUrl(image.src)
                const isVideo = isVideoFile(processedSrc)

                console.log('Processing carousel item:', {
                  originalSrc: image.src,
                  processedSrc,
                  isVideo,
                  alt: image.alt,
                })

                return (
                  <div
                    key={index}
                    className="flex-shrink-0 pr-4 last:pr-0"
                    style={{ width: `${100 / totalSlides}%` }}
                  >
                    <div className="space-y-3">
                      <div className="relative overflow-hidden">
                        {isVideo ? (
                          <CarouselVideo
                            src={processedSrc}
                            alt={image.alt}
                            className="mt-0 mb-0 h-auto w-full object-contain transition-all duration-500 ease-out rounded-sm"
                          />
                        ) : (
                          <Image
                            src={processedSrc}
                            alt={image.alt}
                            width={800}
                            height={600}
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            className="mt-0 mb-0 h-auto w-full object-contain transition-all duration-500 ease-out rounded-sm"
                            loading="lazy"
                            quality={90}
                            blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj48c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjNmNGY2O3N0b3Atb3BhY2l0eToxIiAvPjxzdG9wIG9mZnNldD0iNTAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZTVlN2ViO3N0b3Atb3BhY2l0eToxIiAvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6I2YzZjRmNjtzdG9wLW9wYWNpdHk6MSIgLz48L2xpbmVhckdyYWRpZW50PjxmaWx0ZXIgaWQ9ImJsdXIiPjxmZUdhdXNzaWFuQmx1ciBzdGREZXZpYXRpb249IjIiLz48L2ZpbHRlcj48L2RlZnM+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNncmFkKSIgZmlsdGVyPSJ1cmwoI2JsdXIpIi8+PC9zdmc+"
                          />
                        )}
                      </div>
                      {image.caption && (
                        <p className="mt-0.5 text-base leading-relaxed text-zinc-700 dark:text-zinc-200">
                          {image.caption}
                        </p>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
