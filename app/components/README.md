# Components Documentation

## SideBySideImages

A responsive component that displays two images side by side on larger screens and stacked vertically on mobile devices.

### Usage in Markdown files

You can use the following syntax in your markdown files to create a side-by-side image layout:

```markdown
!side-by-side[Left image alt text|/path/to/left-image.jpg|Right image alt text|/path/to/right-image.jpg]
!side-by-side[Left image alt text|/path/to/left-image.jpg|Right image alt text|/path/to/right-image.jpg] (mid)
!side-by-side[Left image alt text|/path/to/left-image.jpg|Right image alt text|/path/to/right-image.jpg] (wide)
```

#### Parameters

1. `Left image alt text`: The alt text for the left image (or top image on mobile)
2. `path/to/left-image.jpg`: The path to the left image file
3. `Right image alt text`: The alt text for the right image (or bottom image on mobile)
4. `path/to/right-image.jpg`: The path to the right image file
5. `(mid)` or `(wide)` (optional): Size modifier for the container width

#### Size Options

- **Default**: Max width of 1200px, centered with auto margins
- **Mid**: Max width of 1200px with full viewport width behavior (similar to mid-image class)
- **Wide**: Max width of 1632px with full viewport width behavior (similar to wide-image class)

#### Examples

```markdown
!side-by-side[UI before redesign|/images/redesign-before.jpg|UI after redesign|/images/redesign-after.jpg]
!side-by-side[Mobile view|/images/mobile.jpg|Desktop view|/images/desktop.jpg] (mid)
!side-by-side[Original design|/images/original.jpg|Final design|/images/final.jpg] (wide)
```

### How it works

The component:

- Creates a responsive container that uses Flexbox
- Switches from row to column layout on mobile (under 768px)
- Handles image loading with a shimmer effect while images are loading
- Optimizes images using responsive sizes
- Preserves image aspect ratios
- Adds proper accessibility attributes

## Other Image Components

### Standard Images

Standard images can be added using standard markdown syntax:

```markdown
![Alt text](/path/to/image.jpg)
```

### Wide Images

To create a wide image that extends beyond the standard content width:

```markdown
![Alt text (wide)](/path/to/image.jpg)
```

or

```markdown
![Alt text:wide](/path/to/image.jpg)
```

or

```markdown
![Alt text[wide]](/path/to/image.jpg)
```

## Work Links (External/Custom URLs)

To create links to external websites or any custom URL with an image, title, and subtitle:

```markdown
!worklink[Title|Subtitle|/path/to/image.jpg|https://example.com]
```

Features:

- External links (starting with http/https) automatically open in new tabs
- Internal links stay in the same tab
- External link icon appears for external URLs
- Same visual styling as WorkSection thumbnails
- Automatic image URL processing (adds /images/ prefix if needed)

## Component Documentation

### MDXContent.tsx

This component handles rendering Markdown with custom components for images, work links, and side-by-side images. It processes special syntax in markdown and hydrates them with React components on the client side.

### MDXImage.tsx and OptimizedImage.tsx

These components handle optimized image rendering with proper responsive behavior, lazy loading, and sizing.

### WorkLink.tsx

This component creates a card-like link to any external website or custom URL with an image, title, and subtitle. It automatically detects external links and opens them in new tabs with proper security attributes.

Usage in markdown:

```md
!worklink[Title|Subtitle|/images/image-path.jpg|https://example.com]
```

Features:

- External links automatically open in new tabs
- Internal links stay in the same tab
- External link icon appears for external URLs
- Same visual styling as WorkSection thumbnails
- **Automatically groups consecutive links into a 2-column grid layout**
- **Single links display with full width**
- **Responsive design: grid becomes single column on mobile**

### SideBySideImages.tsx

This component displays two images side by side with responsive behavior (stacking on mobile).

Usage in markdown:

```md
!side-by-side[Left image alt text|/images/left-image.jpg|Right image alt text|/images/right-image.jpg]
!side-by-side[Left image alt text|/images/left-image.jpg|Right image alt text|/images/right-image.jpg] (mid)
!side-by-side[Left image alt text|/images/left-image.jpg|Right image alt text|/images/right-image.jpg] (wide)
```

### WorkSummary.tsx

This component creates a summary section for work posts that displays key information about the project like product type, timeline, role, skills, and team members. When there are 4 or more items, it automatically arranges them in a responsive 3-column grid layout on desktop/tablet screens, while maintaining a single-column layout on mobile devices.

Usage in markdown:

```md
!worksummary[Label1:Value1|Label2:Value2|Label3:Value3]
```

Example:

```md
!worksummary[Product:Web application|Skills:UX, UI, Interaction Design|Role:Product Design Lead|Timeline:2018-2023|Team:Sam Marks, Toby Margetts]
```

The component is fully customizable - you can add as many label-value pairs as needed.

### WorkPoints.tsx

This component creates a 2-column layout for displaying text points with titles and subtitles. It's perfect for highlighting key features, benefits, or project outcomes. The component is responsive, displaying in 2 columns on desktop and 1 column on mobile devices.

Usage in markdown:

```md
!workpoints[Title1:Subtitle1|Title2:Subtitle2|Title3:Subtitle3]
```

Example:

```md
!workpoints[User Research:Conducted interviews with 15 users to understand pain points|Information Architecture:Restructured navigation based on card sorting results|Visual Design:Created a cohesive design system with 40+ components|Usability Testing:Validated designs with 8 participants across 3 iterations]
```

### WorkStats.tsx

This component displays statistics in a responsive grid layout with large numbers/percentages and descriptive text below. It supports 2-column layout for 2 stats, 3-column layout for 3 stats, and automatically stacks additional stats in rows with a maximum of 3 columns per row.

Usage in markdown:

```md
!workstats[Value1:Description1|Value2:Description2|Value3:Description3]
```

Example:

```md
!workstats[38%:people reported difficulty in discovering APIs they require|30%:people reported lack of knowledge of existing resources|29%:people reported lack of available time to spend in discovery of those resources]
```

**Key Features:**

- **Responsive grid layout** that adapts based on the number of stats
- **Large, prominent numbers** with smaller descriptive text below
- **Left-aligned text** for better readability
- **Supports 2, 3, or more stats** with automatic column management
- **Dark mode support** with appropriate color schemes

The component supports:

- 2-column layout on desktop, single column on mobile
- Stacking multiple WorkPoints components
- Dark mode support
- Clean typography with proper spacing

### WorkHighlight.tsx

This component creates a highlighted pullout section with an icon and content. It's perfect for emphasizing important information, key insights, or special notes within your work posts.

Usage in markdown:

```md
!workhighlight[Content to highlight]
```

Example:

```md
!workhighlight[Through workshops and stakeholder alignment, we mapped the challenges, identified inefficiencies, and co-created solutions for faster activation.]
```

**Key Features:**

- **Blue-themed highlight box** with subtle background and border
- **Information icon** to draw attention to the content
- **HTML content support** for rich formatting (bold, italic, links, etc.)
- **Responsive design** with appropriate padding on mobile and desktop
- **Dark mode support** with appropriate color schemes

The component supports:

- HTML parsing for rich content formatting
- Responsive padding and spacing
- Dark mode with blue color scheme
- Clean typography with proper line height

### WorkCarousel.tsx

This component creates a Figma Sites-style carousel that displays images in a horizontal scrolling layout. It shows 2 full images and a partial third image, exactly matching the design and functionality of the Figma Sites carousel.

Usage in markdown:

```md
!carousel[/images/image1.jpg|Alt text 1|Caption 1|/images/image2.jpg|Alt text 2|Caption 2|/images/image3.jpg|Alt text 3|Caption 3]
```

Example:

```md
!carousel[/images/figma-sites-1.jpg|Figma Sites interface|Pre-built interactions help you bring motion to your designs|/images/figma-sites-2.jpg|Responsive preview|View a fully responsive site before publishing|/images/figma-sites-3.jpg|Ready-to-use blocks|Ready-to-use blocks help you get started|/images/figma-sites-4.jpg|AI-powered features|Coming soon, code layers will let you turn designs into interactive experiences]
```

Features:

- Shows 2.2 images at a time (2 full + partial third)
- Navigation arrows on the top right
- Slide counter (1/4 format) on the top left
- Smooth horizontal scrolling animation
- Optional captions for each image
- Responsive design
- Dark mode support
- Disabled state for navigation when at start/end

### WorkSection.tsx and WorkSectionServer.tsx

These components handle rendering work post cards on the homepage and work index pages.

## Media Components

### MDXImage

The `MDXImage` component renders images and videos in MDX content. It supports:

- Regular images (jpg, png, webp, etc.)
- Video files (mp4, webm, ogg, mov)
- Automatic aspect ratio detection
- Captions
- Wide layout option

Usage in MDX:

```md
![Alt text](/path/to/image.jpg)
![Alt text (wide)](/path/to/image.jpg)
![Alt text:caption=This is a caption](/path/to/image.jpg)
```

For videos, use the same syntax with video files:

```md
![Video description](/path/to/video.mp4)
![Video description (wide)](/path/to/video.mp4)
![Video description:caption=This is a video](/path/to/video.mp4)
```

### SideBySideImages

The `SideBySideImages` component renders two images or videos side by side. It supports:

- Regular images (jpg, png, webp, etc.)
- Video files (mp4, webm, ogg, mov)
- Mix of image and video
- Responsive layout (stacks on mobile)

Usage in MDX:

```md
!side-by-side[Left image alt|/path/to/left.jpg|Right image alt|/path/to/right.jpg]
!side-by-side[Left image alt|/path/to/left.jpg|Right image alt|/path/to/right.jpg] (mid)
!side-by-side[Left image alt|/path/to/left.jpg|Right image alt|/path/to/right.jpg] (wide)
```

For videos, use the same syntax with video files:

```md
!side-by-side[Left video alt|/path/to/left.mp4|Right video alt|/path/to/right.mp4]
!side-by-side[Left video alt|/path/to/left.mp4|Right video alt|/path/to/right.mp4] (mid)
!side-by-side[Left video alt|/path/to/left.mp4|Right video alt|/path/to/right.mp4] (wide)
```

You can also mix images and videos:

```md
!side-by-side[Image alt|/path/to/image.jpg|Video alt|/path/to/video.mp4]
!side-by-side[Image alt|/path/to/image.jpg|Video alt|/path/to/video.mp4] (mid)
!side-by-side[Image alt|/path/to/image.jpg|Video alt|/path/to/video.mp4] (wide)
```

### ImageTextSideBySide

The `ImageTextSideBySide` component renders an image alongside text content in a side-by-side layout. It supports:

- Regular images (jpg, png, webp, etc.)
- Video files (mp4, webm, ogg, mov)
- Left or right image positioning
- Optional title and description text
- Responsive layout (stacks on mobile)
- Size variants (default, mid, wide)

Usage in MDX:

```md
<!-- 4-part syntax: imageSrc|imageAlt|description|position -->
!imagetext[/path/to/image.jpg|Image description|This is the description text|left]
!imagetext[/path/to/image.jpg|Image description|This is the description text|right]

<!-- 5-part syntax: imageSrc|imageAlt|title|description|position -->
!imagetext[/path/to/image.jpg|Image description|Feature Title|This is the description text|left]
!imagetext[/path/to/image.jpg|Image description|Feature Title|This is the description text|right]

<!-- With size modifiers -->
!imagetext[/path/to/image.jpg|Image description|Feature Title|This is the description text|left] (mid)
!imagetext[/path/to/image.jpg|Image description|Feature Title|This is the description text|right] (wide)
```

For videos, use the same syntax with video files:

```md
!imagetext[/path/to/video.mp4|Video description|Feature Title|This is the description text|left]
!imagetext[/path/to/video.mp4|Video description|Feature Title|This is the description text|right]
```

Features:

- **Flexible positioning**: Image can be on left or right side
- **Optional title**: 4-part syntax for description only, 5-part for title + description
- **Size variants**: Default (1200px max), mid (1200px full viewport), wide (1632px full viewport)
- **Responsive design**: Stacks vertically on mobile, side-by-side on desktop
- **Video support**: Automatically detects and renders video files with proper attributes
- **Image optimization**: Uses DPI-optimized images with AVIF format and responsive srcsets
- **Dark mode support**: Proper color schemes for both light and dark themes

### OptimizedImage

The `OptimizedImage` component optimizes images for performance. It also supports:

- GIF to video conversion for better performance
- Automatic image format conversion (to WebP in production)
- Responsive sizing
- Lazy loading

Usage in React:

```tsx
<OptimizedImage
  src="/path/to/image.jpg"
  alt="Description"
  width={800}
  height={600}
  treatGifAsVideo={true} // default is true
/>
```

## Cover Images

Cover images for blog posts and work items are specified in the frontmatter:

```md
---
title: 'Post Title'
coverImage: '/images/thumb_image.jpg'
---
```

GIF and MP4 files are also supported as cover images.
