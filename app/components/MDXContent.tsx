'use client'

import React, { useEffect, useRef } from 'react'
import { WorkLink, WorkLinksGrid } from './WorkLink'
import { SideBySideImages } from './SideBySideImages'
import { WorkSummary } from './WorkSummary'
import {
  WorkPoints,
  WorkPointsThreeCol,
  WorkPointsMid,
  WorkPointsThreeColMid,
} from './WorkPoints'
import { WorkStats } from './WorkStats'
import { WorkCarousel } from './WorkCarousel'
import { WorkHighlight } from './WorkHighlight'
import { ImageTextSideBySide } from './ImageTextSideBySide'
import { MDXImage } from './MDXImage'
import { createRoot } from 'react-dom/client'

type MDXContentProps = {
  content: string
}

export function MDXContent({ content }: MDXContentProps) {

  // Function to transform markdown work link syntax to special HTML
  // that will be hydrated with the WorkLink component on the client
  const processWorkLinks = (htmlContent: string): string => {
    // Match pattern: !worklink[title|subtitle|image|url]
    // With optional subtitle
    const workLinkRegex = /!worklink\[(.*?)(?:\|(.*?))?\|(.*?)\|(.*?)\]/g

    return htmlContent.replace(
      workLinkRegex,
      (_match, title, subtitle, image, url) => {
        // Create a placeholder with data attributes for client-side hydration
        return `<div
          class="work-link-wrapper"
          data-title="${title || ''}"
          data-subtitle="${subtitle || ''}"
          data-image="${image || ''}"
          data-url="${url || ''}"
        ></div>`
      },
    )
  }

  // Function to transform markdown image syntax to React component placeholders
  const processMarkdownImages = (htmlContent: string): string => {
    // Match all <img> tags
    const imgRegex = /<img.*?src="([^"]*)".*?alt="([^"]*)".*?>/g

    return htmlContent.replace(imgRegex, (_match, src, alt) => {
      // Process the image source by adding /images prefix if needed
      let processedSrc = src

      // If src starts with /work/ change it to /images/work/
      if (src.startsWith('/work/')) {
        processedSrc = `/images${src}`
      }

      // If src starts with / but not with /images/, add /images prefix
      else if (src.startsWith('/') && !src.startsWith('/images/')) {
        processedSrc = `/images${src}`
      }

      // If src doesn't start with /, prepend /images/
      else if (!src.startsWith('/')) {
        processedSrc = `/images/${src}`
      }

      // Extract caption if present (format: :caption=This is the caption)
      const captionMatch = alt.match(/:caption=([^:]+)/)
      const caption = captionMatch ? captionMatch[1].trim() : ''

      // Create a placeholder with data attributes for client-side hydration with MDXImage component
      return `<div
        class="mdx-image-wrapper"
        data-src="${processedSrc}"
        data-alt="${alt}"
        data-caption="${caption}"
      ></div>`
    })
  }



  // Function to transform side-by-side image syntax to special HTML
  const processSideBySideImages = (htmlContent: string): string => {
    // Match pattern: !side-by-side[alt1|src1|caption1|alt2|src2|caption2] (6-part with captions)
    // or !side-by-side[alt1|src1|alt2|src2] (4-part backward compatibility)
    // with optional size modifiers
    const sideBySideRegex =
      /!side-by-side\[(.*?)\|(.*?)\|(.*?)\|(.*?)(?:\|(.*?)\|(.*?))?\](\s*\((wide|mid)\))?/g

    return htmlContent.replace(
      sideBySideRegex,
      (_match, alt1, src1, part3, part4, part5, part6, _sizeMatch, size) => {
        // Determine if this is 4-part or 6-part syntax
        let alt2, src2, caption1 = '', caption2 = ''

        if (part5 && part6) {
          // 6-part syntax: alt1|src1|caption1|alt2|src2|caption2
          caption1 = part3
          alt2 = part4
          src2 = part5
          caption2 = part6
        } else {
          // 4-part syntax: alt1|src1|alt2|src2
          alt2 = part3
          src2 = part4
        }

        // Process the image sources
        const processedSrc1 = processMediaSource(src1)
        const processedSrc2 = processMediaSource(src2)

        // Check if these are video files
        const isVideo1 = processedSrc1.match(/\.(mp4|webm|ogg|mov)($|\?)/i)
        const isVideo2 = processedSrc2.match(/\.(mp4|webm|ogg|mov)($|\?)/i)

        // Determine size class
        const isWide = size === 'wide'
        const isMid = size === 'mid'
        const sizeClass = isWide
          ? 'side-by-side-wide'
          : isMid
            ? 'side-by-side-mid'
            : 'side-by-side-default'

        // Create a placeholder with data attributes for client-side hydration
        return `<div
        class="side-by-side-image-wrapper ${sizeClass}"
        data-alt1="${alt1 || ''}"
        data-src1="${processedSrc1 || ''}"
        data-caption1="${caption1 || ''}"
        data-alt2="${alt2 || ''}"
        data-src2="${processedSrc2 || ''}"
        data-caption2="${caption2 || ''}"
        data-is-video1="${Boolean(isVideo1)}"
        data-is-video2="${Boolean(isVideo2)}"
        data-wide="${isWide}"
        data-mid="${isMid}"
        style="min-height:200px;"
      >
        <noscript>
          <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
              ${
                isVideo1
                  ? `<video
                  src="${processedSrc1}"
                  autoplay
                  loop
                  muted
                  playsinline
                  class="w-full rounded-sm"
                  width="600"
                  height="400"
                >
                  <p>${alt1 || 'Your browser does not support the video tag.'}</p>
                </video>`
                  : `<img
                  src="${processedSrc1}"
                  alt="${alt1 || ''}"
                  class="w-full rounded-sm"
                  loading="lazy"
                  width="600"
                  height="400"
                />`
              }
              ${caption1 ? `<figcaption class="mt-2 text-sm text-zinc-700 dark:text-zinc-200">${caption1}</figcaption>` : ''}
            </div>
            <div class="flex-1">
              ${
                isVideo2
                  ? `<video
                  src="${processedSrc2}"
                  autoplay
                  loop
                  muted
                  playsinline
                  class="w-full rounded-sm"
                  width="600"
                  height="400"
                >
                  <p>${alt2 || 'Your browser does not support the video tag.'}</p>
                </video>`
                  : `<img
                  src="${processedSrc2}"
                  alt="${alt2 || ''}"
                  class="w-full rounded-sm"
                  loading="lazy"
                  width="600"
                  height="400"
                />`
              }
              ${caption2 ? `<figcaption class="mt-2 text-sm text-zinc-700 dark:text-zinc-200">${caption2}</figcaption>` : ''}
            </div>
          </div>
        </noscript>
      </div>`
      },
    )
  }

  // Helper function to process media source paths
  const processMediaSource = (src: string): string => {
    // If src starts with /work/ change it to /images/work/
    if (src.startsWith('/work/')) {
      return `/images${src}`
    }

    // If src starts with / but not with /images/, add /images prefix
    if (src.startsWith('/') && !src.startsWith('/images/')) {
      return `/images${src}`
    }

    // If src doesn't start with /, prepend /images/
    if (!src.startsWith('/')) {
      return `/images/${src}`
    }

    return src
  }

  // Function to transform work summary syntax to special HTML
  const processWorkSummary = (htmlContent: string): string => {
    // Match pattern: !worksummary[label1:value1|label2:value2|...]
    // Use a simpler regex that captures all content between brackets
    const workSummaryRegex = /!worksummary\[([\s\S]*?)\]/g

    return htmlContent.replace(workSummaryRegex, (_match, content) => {
      // Split the content by pipe character - all parts are now label:value pairs
      const pairs = content.split('|').filter((part: string) => part.trim())

      // Create a placeholder with data attributes for client-side hydration
      return `<div
        class="work-summary-wrapper"
        data-pairs="${encodeURIComponent(JSON.stringify(pairs))}"
        style="margin:2rem 0;"
      ></div>`
    })
  }

  // Function to transform work points syntax to special HTML
  const processWorkPoints = (htmlContent: string): string => {
    // Match pattern: !workpoints[title1:subtitle1|title2:subtitle2|...]
    // Use word boundary to ensure we only match exact "workpoints" and not variants
    const workPointsRegex = /!workpoints(?!threecol|mid)\[([\s\S]*?)\]/g

    return htmlContent.replace(workPointsRegex, (_match, content) => {
      // Split the content by pipe character - all parts are now title:subtitle pairs
      const pairs = content.split('|').filter((part: string) => part.trim())

      // Create a placeholder with data attributes for client-side hydration
      return `<div
        class="work-points-wrapper"
        data-pairs="${encodeURIComponent(JSON.stringify(pairs))}"
        style="margin:2rem 0;"
      ></div>`
    })
  }

  // Function to transform work points three column syntax to special HTML
  const processWorkPointsThreeCol = (htmlContent: string): string => {
    // Match pattern: !workpointsthreecol[title1:subtitle1|title2:subtitle2|...]
    const workPointsThreeColRegex = /!workpointsthreecol\[([\s\S]*?)\]/g

    return htmlContent.replace(workPointsThreeColRegex, (_match, content) => {
      // Split the content by pipe character - all parts are now title:subtitle pairs
      const pairs = content.split('|').filter((part: string) => part.trim())

      // Create a placeholder with data attributes for client-side hydration
      return `<div
        class="work-points-threecol-wrapper"
        data-pairs="${encodeURIComponent(JSON.stringify(pairs))}"
        style="margin:2rem 0;"
      ></div>`
    })
  }

  // Function to transform work points mid syntax to special HTML
  const processWorkPointsMid = (htmlContent: string): string => {
    // Match pattern: !workpointsmid[title1:subtitle1|title2:subtitle2|...]
    const workPointsMidRegex = /!workpointsmid\[([\s\S]*?)\]/g

    return htmlContent.replace(workPointsMidRegex, (_match, content) => {
      // Split the content by pipe character - all parts are now title:subtitle pairs
      const pairs = content.split('|').filter((part: string) => part.trim())

      // Create a placeholder with data attributes for client-side hydration
      return `<div
        class="work-points-mid-wrapper"
        data-pairs="${encodeURIComponent(JSON.stringify(pairs))}"
        style="margin:2rem 0;"
      ></div>`
    })
  }

  // Function to transform work points three column mid syntax to special HTML
  const processWorkPointsThreeColMid = (htmlContent: string): string => {
    // Match pattern: !workpointsthreecolmid[title1:subtitle1|title2:subtitle2|...]
    const workPointsThreeColMidRegex = /!workpointsthreecolmid\[([\s\S]*?)\]/g

    return htmlContent.replace(
      workPointsThreeColMidRegex,
      (_match, content) => {
        // Split the content by pipe character - all parts are now title:subtitle pairs
        const pairs = content.split('|').filter((part: string) => part.trim())

        // Create a placeholder with data attributes for client-side hydration
        return `<div
        class="work-points-threecolmid-wrapper"
        data-pairs="${encodeURIComponent(JSON.stringify(pairs))}"
        style="margin:2rem 0;"
      ></div>`
      },
    )
  }

  // Function to transform work stats syntax to special HTML
  const processWorkStats = (htmlContent: string): string => {
    // Match pattern: !workstats[value1:description1|value2:description2|...]
    // Use a simpler regex that captures all content between brackets
    const workStatsRegex = /!workstats\[([\s\S]*?)\]/g

    return htmlContent.replace(workStatsRegex, (_match, content) => {
      // Split the content by pipe character - all parts are now value:description pairs
      const pairs = content.split('|').filter((part: string) => part.trim())

      // Create a placeholder with data attributes for client-side hydration
      return `<div
        class="work-stats-wrapper"
        data-pairs="${encodeURIComponent(JSON.stringify(pairs))}"
      ></div>`
    })
  }

  // Function to transform work highlight syntax to special HTML
  const processWorkHighlight = (htmlContent: string): string => {
    // Match pattern: !workhighlight[content]
    const workHighlightRegex = /!workhighlight\[([\s\S]*?)\]/g

    return htmlContent.replace(workHighlightRegex, (_match, content) => {
      // Create a placeholder with data attributes for client-side hydration
      return `<div
        class="work-highlight-wrapper"
        data-content="${encodeURIComponent(content.trim())}"
        style="margin:2rem 0;"
      ></div>`
    })
  }

  // Function to transform carousel syntax to special HTML
  const processCarousel = (htmlContent: string): string => {
    // Match pattern: !carousel[src1|alt1|caption1|src2|alt2|caption2|...]
    // Use a simpler regex that captures all content between brackets
    const carouselRegex = /!carousel\[([\s\S]*?)\]/g

    return htmlContent.replace(carouselRegex, (_match, content) => {
      // Split the content by pipe character
      const parts = content.split('|').filter((part: string) => part.trim())

      // Group parts into sets of 3 (src, alt, caption)
      const images = []
      for (let i = 0; i < parts.length; i += 3) {
        if (parts[i] && parts[i + 1]) {
          images.push({
            src: parts[i].trim(),
            alt: parts[i + 1].trim(),
            caption: parts[i + 2] ? parts[i + 2].trim() : '',
          })
        }
      }

      // Create a placeholder with data attributes for client-side hydration
      return `<div
        class="work-carousel-wrapper"
        data-images="${encodeURIComponent(JSON.stringify(images))}"
        style="min-height:300px;"
      ></div>`
    })
  }

  // Function to transform image-text side-by-side syntax to special HTML
  const processImageTextSideBySide = (htmlContent: string): string => {
    // Match pattern: !imagetext[imageSrc|imageAlt|title|description|position] (5-part)
    // or !imagetext[imageSrc|imageAlt|description|position] (4-part without title)
    // with optional size modifiers
    const imageTextRegex =
      /!imagetext\[(.*?)\|(.*?)\|(.*?)\|(.*?)(?:\|(.*?))?\](\s*\((wide|mid)\))?/g

    return htmlContent.replace(
      imageTextRegex,
      (_match, imageSrc, imageAlt, part3, part4, part5, _sizeMatch, size) => {
        // Determine if this is 4-part or 5-part syntax
        let title = '', description = '', position = 'left'

        if (part5) {
          // 5-part syntax: imageSrc|imageAlt|title|description|position
          title = part3
          description = part4
          position = part5
        } else {
          // 4-part syntax: imageSrc|imageAlt|description|position
          description = part3
          position = part4
        }

        // Process the image source
        const processedSrc = processMediaSource(imageSrc)

        // Determine size class
        const isWide = size === 'wide'
        const isMid = size === 'mid'
        const sizeClass = isWide
          ? 'image-text-wide'
          : isMid
            ? 'image-text-mid'
            : 'image-text-default'

        // Create a placeholder with data attributes for client-side hydration
        return `<div
          class="image-text-wrapper ${sizeClass}"
          data-image-src="${processedSrc || ''}"
          data-image-alt="${imageAlt || ''}"
          data-title="${title || ''}"
          data-description="${description || ''}"
          data-position="${position || 'left'}"
          data-wide="${isWide}"
          data-mid="${isMid}"
          style="min-height:200px;"
        ></div>`
      },
    )
  }

  // Process the content: work links, then side-by-side images, then image-text, then work summary, then work points variants (most specific first), then work stats, then work highlight, then carousel, then regular images
  const processedWithWorkLinks = processWorkLinks(content)
  const processedWithSideBySideImages = processSideBySideImages(
    processedWithWorkLinks,
  )
  const processedWithImageText = processImageTextSideBySide(
    processedWithSideBySideImages,
  )
  const processedWithWorkSummary = processWorkSummary(
    processedWithImageText,
  )
  // Process most specific workpoints patterns first to avoid conflicts
  const processedWithWorkPointsThreeColMid = processWorkPointsThreeColMid(
    processedWithWorkSummary,
  )
  const processedWithWorkPointsThreeCol = processWorkPointsThreeCol(
    processedWithWorkPointsThreeColMid,
  )
  const processedWithWorkPointsMid = processWorkPointsMid(
    processedWithWorkPointsThreeCol,
  )
  const processedWithWorkPoints = processWorkPoints(processedWithWorkPointsMid)
  const processedWithWorkStats = processWorkStats(processedWithWorkPoints)
  const processedWithWorkHighlight = processWorkHighlight(processedWithWorkStats)
  const processedWithCarousel = processCarousel(processedWithWorkHighlight)
  const processedContent = processMarkdownImages(processedWithCarousel)

  // Use ref to persist roots across re-renders for component hydration (WorkLink, WorkSummary, etc.)
  const rootsRef = useRef(new Map<Element, ReturnType<typeof createRoot>>())
  const isUnmountingRef = useRef(false)

  // Client-side effect to hydrate component placeholders (WorkLink, WorkSummary, etc.)
  useEffect(() => {

    // Reset unmounting flag
    isUnmountingRef.current = false

    // Get the persistent roots map
    const roots = rootsRef.current

    // Declare timeout ID for cleanup
    let timeoutId: NodeJS.Timeout

    // Helper function to create or reuse a root
    const getOrCreateRoot = (container: Element) => {
      // Don't create roots if we're in the process of unmounting
      if (isUnmountingRef.current) {
        return null
      }

      if (roots.has(container)) {
        const existingRoot = roots.get(container)!
        // Verify the container still has the root marker
        if (container.hasAttribute('data-react-root')) {
          return existingRoot
        } else {
          // Root marker was removed, clean up the stale root
          try {
            existingRoot.unmount()
          } catch (error) {
            console.warn('Error unmounting stale root:', error)
          }
          roots.delete(container)
        }
      }

      // Check if the container already has a React root using our data attribute
      // This prevents the "createRoot() called on container that already has a root" error
      if (container.hasAttribute('data-react-root')) {
        // Container already has a root, but it's not in our current map
        // This can happen when the effect re-runs. Skip creating a new root.
        console.warn('Container already has a React root, skipping creation')
        return null
      }

      try {
        const root = createRoot(container)
        roots.set(container, root)
        // Mark the container as having a React root
        container.setAttribute('data-react-root', 'true')
        return root
      } catch (error) {
        console.error('Error creating React root:', error)
        return null
      }
    }

    // Use requestAnimationFrame to ensure DOM is fully updated after dangerouslySetInnerHTML
    // This fixes the issue where components don't load during client-side navigation
    timeoutId = setTimeout(() => {
      requestAnimationFrame(() => {
        // Images are now generated directly in processMarkdownImages, no hydration needed

      // Hydrate work link components with grid grouping
      const workLinkWrappers = Array.from(
        document.querySelectorAll('.work-link-wrapper'),
      )

      // Group consecutive work link wrappers
      const workLinkGroups: Element[][] = []
      let currentGroup: Element[] = []

      workLinkWrappers.forEach((wrapper, index) => {
        const prevWrapper = index > 0 ? workLinkWrappers[index - 1] : null
        const nextWrapper =
          index < workLinkWrappers.length - 1 ? workLinkWrappers[index + 1] : null

        // Check if this wrapper is consecutive with the previous one
        const isConsecutiveWithPrev =
          (prevWrapper && wrapper.previousElementSibling === prevWrapper) ||
          (wrapper.previousElementSibling?.tagName === 'P' &&
            wrapper.previousElementSibling.textContent?.trim() === '' &&
            wrapper.previousElementSibling.previousElementSibling === prevWrapper)

        // Check if this wrapper is consecutive with the next one
        const isConsecutiveWithNext =
          (nextWrapper && wrapper.nextElementSibling === nextWrapper) ||
          (wrapper.nextElementSibling?.tagName === 'P' &&
            wrapper.nextElementSibling.textContent?.trim() === '' &&
            wrapper.nextElementSibling.nextElementSibling === nextWrapper)

        if (isConsecutiveWithPrev || isConsecutiveWithNext) {
          // This is part of a group
          if (currentGroup.length === 0 || !isConsecutiveWithPrev) {
            // Start a new group
            currentGroup = [wrapper]
          } else {
            // Add to current group
            currentGroup.push(wrapper)
          }

          // If this is the last in the group, save it
          if (!isConsecutiveWithNext) {
            workLinkGroups.push([...currentGroup])
            currentGroup = []
          }
        } else {
          // This is a standalone item
          workLinkGroups.push([wrapper])
        }
      })

    // Render each group
    workLinkGroups.forEach((group) => {
      if (group.length === 1) {
        // Single item - render normally
        const wrapper = group[0]
        const title = wrapper.getAttribute('data-title') || ''
        const subtitle = wrapper.getAttribute('data-subtitle') || ''
        const image = wrapper.getAttribute('data-image') || ''
        const url = wrapper.getAttribute('data-url') || ''

        if (title && image && url) {
          wrapper.innerHTML = ''

          // Create a single WorkLink with margin
          const singleLinkContainer = document.createElement('div')
          singleLinkContainer.className = 'my-8'
          wrapper.appendChild(singleLinkContainer)

          const workLink = React.createElement(WorkLink, {
            title,
            subtitle,
            image,
            url,
          })

          const root = getOrCreateRoot(singleLinkContainer)
          if (root) {
            root.render(workLink)
          }
        }
      } else {
        // Multiple items - render in grid
        const firstWrapper = group[0]
        const gridContainer = document.createElement('div')

        // Clear all wrappers in the group and hide all but the first
        group.forEach((wrapper, index) => {
          wrapper.innerHTML = ''
          if (index > 0) {
            ;(wrapper as HTMLElement).style.display = 'none'
          }
        })

        // Add grid container to the first wrapper
        firstWrapper.appendChild(gridContainer)

        // Create WorkLink components for each item in the group
        const workLinks = group
          .map((wrapper, index) => {
            const title = wrapper.getAttribute('data-title') || ''
            const subtitle = wrapper.getAttribute('data-subtitle') || ''
            const image = wrapper.getAttribute('data-image') || ''
            const url = wrapper.getAttribute('data-url') || ''

            if (title && image && url) {
              return React.createElement(WorkLink, {
                key: `worklink-${index}-${title}`,
                title,
                subtitle,
                image,
                url,
              })
            }
            return null
          })
          .filter(Boolean)

        // Render the grid with all WorkLinks
        const gridComponent = React.createElement(WorkLinksGrid, {
          children: workLinks,
        })

        const root = getOrCreateRoot(gridContainer)
        if (root) {
          root.render(gridComponent)
        }
      }
    })

    // Hydrate side-by-side image components
    const sideBySideImageWrappers = document.querySelectorAll(
      '.side-by-side-image-wrapper',
    )

    sideBySideImageWrappers.forEach((wrapper) => {
      const src1 = wrapper.getAttribute('data-src1') || ''
      const src2 = wrapper.getAttribute('data-src2') || ''
      const alt1 = wrapper.getAttribute('data-alt1') || ''
      const alt2 = wrapper.getAttribute('data-alt2') || ''
      const caption1 = wrapper.getAttribute('data-caption1') || ''
      const caption2 = wrapper.getAttribute('data-caption2') || ''
      const isWide = wrapper.getAttribute('data-wide') === 'true'
      const isMid = wrapper.getAttribute('data-mid') === 'true'

      if (src1 && src2) {
        // Create a new side-by-side images component with updated props
        const sideBySideImagesComponent = React.createElement(
          SideBySideImages,
          {
            src1,
            src2,
            alt1,
            alt2,
            caption1,
            caption2,
            isWide,
            isMid,
          },
        )

        // Use the helper function to get or create a root
        const root = getOrCreateRoot(wrapper)
        if (root) {
          root.render(sideBySideImagesComponent)
        }
      }
    })

    // Hydrate image-text side-by-side components
    const imageTextWrappers = document.querySelectorAll('.image-text-wrapper')

    imageTextWrappers.forEach((wrapper) => {
      const imageSrc = wrapper.getAttribute('data-image-src') || ''
      const imageAlt = wrapper.getAttribute('data-image-alt') || ''
      const title = wrapper.getAttribute('data-title') || ''
      const description = wrapper.getAttribute('data-description') || ''
      const position = wrapper.getAttribute('data-position') || 'left'
      const isWide = wrapper.getAttribute('data-wide') === 'true'
      const isMid = wrapper.getAttribute('data-mid') === 'true'

      if (imageSrc && imageAlt && description) {
        // Create a new image-text component
        const imageTextComponent = React.createElement(
          ImageTextSideBySide,
          {
            imageSrc,
            imageAlt,
            title: title || undefined,
            description,
            imagePosition: position as 'left' | 'right',
            isWide,
            isMid,
          },
        )

        // Use the helper function to get or create a root
        const root = getOrCreateRoot(wrapper)
        if (root) {
          root.render(imageTextComponent)
        }
      }
    })

    // Hydrate work summary wrappers
    const workSummaryWrappers = document.querySelectorAll(
      '.work-summary-wrapper',
    )

    workSummaryWrappers.forEach((wrapper) => {
      const pairsJson = wrapper.getAttribute('data-pairs') || '[]'

      try {
        // Parse the pairs from the JSON string
        const pairs = JSON.parse(decodeURIComponent(pairsJson))

        // Process pairs into items array
        const items = pairs.map((pair: string) => {
          // Split by the first colon only
          const colonIndex = pair.indexOf(':')
          if (colonIndex === -1) return { label: pair, value: '' }

          const label = pair.substring(0, colonIndex).trim()
          const value = pair.substring(colonIndex + 1).trim()
          return { label, value }
        })

        // Use the helper function to get or create a root
        const root = getOrCreateRoot(wrapper)
        if (root) {
          root.render(<WorkSummary items={items} />)
        }
      } catch (error) {
        console.error('Error hydrating WorkSummary component:', error)
        wrapper.innerHTML = `<div class="error-message p-4 bg-red-100 text-red-700 rounded">Error rendering work summary</div>`
      }
    })

    // Hydrate work points wrappers
    const workPointsWrappers = document.querySelectorAll('.work-points-wrapper')

    workPointsWrappers.forEach((wrapper) => {
      const pairsJson = wrapper.getAttribute('data-pairs') || '[]'

      try {
        // Parse the pairs from the JSON string
        const pairs = JSON.parse(decodeURIComponent(pairsJson))

        // Process pairs into items array
        const items = pairs.map((pair: string) => {
          // Split by the first colon only
          const colonIndex = pair.indexOf(':')
          if (colonIndex === -1) return { title: pair, subtitle: '' }

          const title = pair.substring(0, colonIndex).trim()
          const subtitle = pair.substring(colonIndex + 1).trim()
          return { title, subtitle }
        })

        // Use the helper function to get or create a root
        const root = getOrCreateRoot(wrapper)
        if (root) {
          root.render(<WorkPoints items={items} />)
        }
      } catch (error) {
        console.error('Error hydrating WorkPoints component:', error)
        wrapper.innerHTML = `<div class="error-message p-4 bg-red-100 text-red-700 rounded">Error rendering work points</div>`
      }
    })

    // Hydrate work points three column wrappers
    const workPointsThreeColWrappers = document.querySelectorAll(
      '.work-points-threecol-wrapper',
    )
    console.log(
      'Found WorkPointsThreeCol wrappers:',
      workPointsThreeColWrappers.length,
    )

    workPointsThreeColWrappers.forEach((wrapper) => {
      const pairsJson = wrapper.getAttribute('data-pairs') || '[]'
      console.log('Processing WorkPointsThreeCol wrapper with data:', pairsJson)

      try {
        // Parse the pairs from the JSON string
        const pairs = JSON.parse(decodeURIComponent(pairsJson))
        console.log('Parsed pairs for WorkPointsThreeCol:', pairs)

        // Process pairs into items array
        const items = pairs.map((pair: string) => {
          // Split by the first colon only
          const colonIndex = pair.indexOf(':')
          if (colonIndex === -1) return { title: pair, subtitle: '' }

          const title = pair.substring(0, colonIndex).trim()
          const subtitle = pair.substring(colonIndex + 1).trim()
          return { title, subtitle }
        })

        console.log('Processed items for WorkPointsThreeCol:', items)

        // Use the helper function to get or create a root
        const root = getOrCreateRoot(wrapper)
        if (root) {
          console.log('Rendering WorkPointsThreeCol component')
          root.render(<WorkPointsThreeCol items={items} />)
        } else {
          console.error('Failed to create root for WorkPointsThreeCol')
        }
      } catch (error) {
        console.error(
          'Error hydrating WorkPointsThreeCol component:',
          error,
          'Data:',
          pairsJson,
        )
        wrapper.innerHTML = `<div class="error-message p-4 bg-red-100 text-red-700 rounded">Error rendering work points three column: ${error instanceof Error ? error.message : 'Unknown error'}</div>`
      }
    })

    // Hydrate work points mid wrappers
    const workPointsMidWrappers = document.querySelectorAll(
      '.work-points-mid-wrapper',
    )
    console.log('Found WorkPointsMid wrappers:', workPointsMidWrappers.length)

    workPointsMidWrappers.forEach((wrapper) => {
      const pairsJson = wrapper.getAttribute('data-pairs') || '[]'

      try {
        // Parse the pairs from the JSON string
        const pairs = JSON.parse(decodeURIComponent(pairsJson))

        // Process pairs into items array
        const items = pairs.map((pair: string) => {
          // Split by the first colon only
          const colonIndex = pair.indexOf(':')
          if (colonIndex === -1) return { title: pair, subtitle: '' }

          const title = pair.substring(0, colonIndex).trim()
          const subtitle = pair.substring(colonIndex + 1).trim()
          return { title, subtitle }
        })

        // Use the helper function to get or create a root
        const root = getOrCreateRoot(wrapper)
        if (root) {
          root.render(<WorkPointsMid items={items} />)
        }
      } catch (error) {
        console.error('Error hydrating WorkPointsMid component:', error)
        wrapper.innerHTML = `<div class="error-message p-4 bg-red-100 text-red-700 rounded">Error rendering work points mid</div>`
      }
    })

    // Hydrate work points three column mid wrappers
    const workPointsThreeColMidWrappers = document.querySelectorAll(
      '.work-points-threecolmid-wrapper',
    )
    console.log(
      'Found WorkPointsThreeColMid wrappers:',
      workPointsThreeColMidWrappers.length,
    )

    workPointsThreeColMidWrappers.forEach((wrapper) => {
      const pairsJson = wrapper.getAttribute('data-pairs') || '[]'

      try {
        // Parse the pairs from the JSON string
        const pairs = JSON.parse(decodeURIComponent(pairsJson))

        // Process pairs into items array
        const items = pairs.map((pair: string) => {
          // Split by the first colon only
          const colonIndex = pair.indexOf(':')
          if (colonIndex === -1) return { title: pair, subtitle: '' }

          const title = pair.substring(0, colonIndex).trim()
          const subtitle = pair.substring(colonIndex + 1).trim()
          return { title, subtitle }
        })

        // Use the helper function to get or create a root
        const root = getOrCreateRoot(wrapper)
        if (root) {
          root.render(<WorkPointsThreeColMid items={items} />)
        }
      } catch (error) {
        console.error('Error hydrating WorkPointsThreeColMid component:', error)
        wrapper.innerHTML = `<div class="error-message p-4 bg-red-100 text-red-700 rounded">Error rendering work points three column mid</div>`
      }
    })

    // Hydrate work stats wrappers
    const workStatsWrappers = document.querySelectorAll('.work-stats-wrapper')

    workStatsWrappers.forEach((wrapper) => {
      const pairsJson = wrapper.getAttribute('data-pairs') || '[]'

      try {
        // Parse the pairs from the JSON string
        const pairs = JSON.parse(decodeURIComponent(pairsJson))

        // Process pairs into items array
        const items = pairs.map((pair: string) => {
          // Split by the first colon only
          const colonIndex = pair.indexOf(':')
          if (colonIndex === -1) return { value: pair, description: '' }

          const value = pair.substring(0, colonIndex).trim()
          const description = pair.substring(colonIndex + 1).trim()
          return { value, description }
        })

        // Use the helper function to get or create a root
        const root = getOrCreateRoot(wrapper)
        if (root) {
          root.render(<WorkStats items={items} />)
        }
      } catch (error) {
        console.error('Error hydrating WorkStats component:', error)
        wrapper.innerHTML = `<div class="error-message p-4 bg-red-100 text-red-700 rounded">Error rendering work stats</div>`
      }
    })

    // Hydrate work highlight wrappers
    const workHighlightWrappers = document.querySelectorAll('.work-highlight-wrapper')

    workHighlightWrappers.forEach((wrapper) => {
      const content = wrapper.getAttribute('data-content') || ''

      try {
        // Decode the content
        const decodedContent = decodeURIComponent(content)

        // Use the helper function to get or create a root
        const root = getOrCreateRoot(wrapper)
        if (root) {
          root.render(<WorkHighlight content={decodedContent} />)
        }
      } catch (error) {
        console.error('Error hydrating WorkHighlight component:', error)
        wrapper.innerHTML = `<div class="error-message p-4 bg-red-100 text-red-700 rounded">Error rendering work highlight</div>`
      }
    })

    // Hydrate carousel wrappers
    const carouselWrappers = document.querySelectorAll('.work-carousel-wrapper')
    console.log('MDXContent: Found carousel wrappers:', carouselWrappers.length)

    carouselWrappers.forEach((wrapper) => {
      const imagesJson = wrapper.getAttribute('data-images') || '[]'
      console.log('MDXContent: Processing carousel with images:', imagesJson)

      try {
        // Parse the images from the JSON string
        const images = JSON.parse(decodeURIComponent(imagesJson))

        // Use the helper function to get or create a root
        const root = getOrCreateRoot(wrapper)
        if (root) {
          root.render(<WorkCarousel images={images} />)
        }
        } catch (error) {
          console.error('Error hydrating WorkCarousel component:', error)
          wrapper.innerHTML = `<div class="error-message p-4 bg-red-100 text-red-700 rounded">Error rendering carousel</div>`
        }
      })

    // Hydrate MDXImage wrappers
    const mdxImageWrappers = document.querySelectorAll('.mdx-image-wrapper')

    mdxImageWrappers.forEach((wrapper) => {
      const src = wrapper.getAttribute('data-src') || ''
      const alt = wrapper.getAttribute('data-alt') || ''
      const caption = wrapper.getAttribute('data-caption') || ''

      if (src && alt) {
        // Use the helper function to get or create a root
        const root = getOrCreateRoot(wrapper)
        if (root) {
          root.render(<MDXImage src={src} alt={alt} caption={caption} />)
        }
      }
    })
      })
    }, 0)

    // Clean up roots when content changes (but not when component unmounts)
    return () => {
      // Clear the timeout if component unmounts before hydration
      clearTimeout(timeoutId)

      // Set unmounting flag to prevent new root creation during cleanup
      isUnmountingRef.current = true

      // Defer root unmounting using setTimeout to avoid race conditions
      // during React's render cycle in Next.js 15.3.0 static sites
      setTimeout(() => {
        roots.forEach((root, container) => {
          try {
            root.unmount()
            // Clear the container's React root marker
            container.removeAttribute('data-react-root')
          } catch (error) {
            // Silently handle cases where root is already unmounted
            console.warn('Root already unmounted:', error)
          }
        })
        // Clear the roots map for the next render
        roots.clear()
      }, 0)
    }
  }, [content.length]) // Use content length as dependency to trigger hydration

  return (
    <div
      className="prose prose-gray dark:prose-invert max-w-none px-0 [&_.wide-image]:w-full [&_.wide-image]:max-w-none [&>p]:mx-0"
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  )
}
