import { Metadata } from 'next'

import { SITE_CONFIG, SEO_CONFIG } from '@/constants'
import { SEOProps } from '@/types'

/**
 * Generate metadata for Next.js pages
 */
export function generateMetadata({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  section,
  tags = [],
}: SEOProps = {}): Metadata {
  const metaTitle = title
    ? `${title} | ${SITE_CONFIG.name}`
    : SEO_CONFIG.defaultTitle

  const metaDescription = description || SEO_CONFIG.defaultDescription
  const metaImage = image || SEO_CONFIG.defaultImage
  const metaUrl = url || SEO_CONFIG.siteUrl

  // Combine keywords and tags
  const allKeywords = [...keywords, ...tags].filter(Boolean)

  return {
    title: metaTitle,
    description: metaDescription,
    keywords: allKeywords.length > 0 ? allKeywords.join(', ') : undefined,
    authors: author ? [{ name: author }] : [{ name: SITE_CONFIG.author.name }],
    creator: SITE_CONFIG.author.name,
    publisher: SITE_CONFIG.author.name,

    // Open Graph
    openGraph: {
      type: type as any,
      locale: SEO_CONFIG.locale,
      url: metaUrl,
      title: metaTitle,
      description: metaDescription,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: metaImage,
          width: 1200,
          height: 630,
          alt: metaTitle,
        },
      ],
      ...(type === 'article' && {
        publishedTime,
        modifiedTime,
        authors: [author || SITE_CONFIG.author.name],
        section,
        tags,
      }),
    },

    // Twitter
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      creator: SEO_CONFIG.twitterHandle,
      site: SEO_CONFIG.twitterHandle,
      images: [
        {
          url: metaImage,
          width: 1200,
          height: 630,
          alt: metaTitle,
        },
      ],
    },

    // Additional meta tags
    other: {
      'theme-color': '#ffffff',
      'color-scheme': 'light dark',
      'format-detection': 'telephone=no',
      'msapplication-TileColor': '#ffffff',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
    },

    // Robots
    robots: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
      'max-video-preview': -1,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

    // Verification
    verification: {
      google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
      yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION,
      yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION,
    },

    // Canonical URL
    alternates: {
      canonical: metaUrl,
    },

    // Application name
    applicationName: SITE_CONFIG.name,

    // Apple Web App
    appleWebApp: {
      title: SITE_CONFIG.name,
      statusBarStyle: 'default',
      capable: true,
    },

    // Icons
    icons: {
      icon: [
        { url: '/favicon.ico', sizes: '16x16 32x32 48x48', type: 'image/x-icon' },
        { url: '/icon.svg', sizes: 'any', type: 'image/svg+xml' },
      ],
      shortcut: '/favicon.ico',
      apple: [
        { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
      ],
    },

    // Manifest
    manifest: '/manifest.json',
  }
}

/**
 * Generate structured data for rich snippets
 */
export function generateStructuredData(
  params: SEOProps & {
    type?: 'website' | 'article' | 'person' | 'organization'
  },
) {
  const {
    type,
    title,
    description,
    image,
    url,
    publishedTime,
    modifiedTime,
    author,
  } = params

  const structuredType: 'website' | 'article' | 'person' | 'organization' =
    type || 'website'
  const baseData = {
    '@context': 'https://schema.org',
    '@type': structuredType === 'website' ? 'WebSite' : 'Article',
    name: title || SITE_CONFIG.title,
    description: description || SEO_CONFIG.defaultDescription,
    url: url || SEO_CONFIG.siteUrl,
    image: image || SEO_CONFIG.defaultImage,
  }

  switch (structuredType as string) {
    case 'article':
      return {
        ...baseData,
        '@type': 'Article',
        headline: title,
        datePublished: publishedTime,
        dateModified: modifiedTime || publishedTime,
        author: {
          '@type': 'Person',
          name: author || SITE_CONFIG.author.name,
          url: SITE_CONFIG.url,
        },
        publisher: {
          '@type': 'Organization',
          name: SITE_CONFIG.name,
          logo: {
            '@type': 'ImageObject',
            url: `${SITE_CONFIG.url}/logo.png`,
          },
        },
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': url || SITE_CONFIG.url,
        },
      }

    case 'person':
      return {
        '@context': 'https://schema.org',
        '@type': 'Person',
        name: SITE_CONFIG.author.name,
        jobTitle: SITE_CONFIG.role,
        worksFor: {
          '@type': 'Organization',
          name: 'Freelance',
        },
        url: SITE_CONFIG.url,
        sameAs: [
          `https://linkedin.com/in/${SITE_CONFIG.author.linkedin}`,
          `https://github.com/${SITE_CONFIG.author.github}`,
          `https://twitter.com/${SITE_CONFIG.author.twitter.replace('@', '')}`,
        ],
        address: {
          '@type': 'PostalAddress',
          addressLocality: SITE_CONFIG.location,
        },
      }

    case 'organization':
      return {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: SITE_CONFIG.name,
        url: SITE_CONFIG.url,
        logo: `${SITE_CONFIG.url}/logo.png`,
        sameAs: [
          `https://linkedin.com/in/${SITE_CONFIG.author.linkedin}`,
          `https://github.com/${SITE_CONFIG.author.github}`,
          `https://twitter.com/${SITE_CONFIG.author.twitter.replace('@', '')}`,
        ],
      }

    default:
      return baseData
  }
}

/**
 * Generate breadcrumb structured data
 */
export function generateBreadcrumbData(
  items: Array<{ name: string; url: string }>,
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  }
}

/**
 * Generate FAQ structured data
 */
export function generateFAQData(
  faqs: Array<{ question: string; answer: string }>,
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map((faq) => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  }
}

/**
 * Generate portfolio/creative work structured data
 */
export function generateCreativeWorkData({
  title,
  description,
  image,
  url,
  dateCreated,
  creator,
  keywords = [],
}: {
  title: string
  description: string
  image: string
  url: string
  dateCreated?: string
  creator?: string
  keywords?: string[]
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'CreativeWork',
    name: title,
    description,
    image,
    url,
    dateCreated,
    creator: {
      '@type': 'Person',
      name: creator || SITE_CONFIG.author.name,
    },
    keywords: keywords.join(', '),
    inLanguage: 'en-US',
  }
}

/**
 * Component for injecting structured data
 */
export function StructuredData({ data }: { data: object }) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  )
}

/**
 * Utility to generate canonical URL
 */
export function getCanonicalUrl(path: string): string {
  const baseUrl = SEO_CONFIG.siteUrl.replace(/\/$/, '')
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl}${cleanPath}`
}

/**
 * Utility to generate Open Graph image URL
 */
export function getOGImageUrl(title?: string, description?: string): string {
  if (!title) return SEO_CONFIG.defaultImage

  // If you have a dynamic OG image service, you can use it here
  // For now, return the default image
  return SEO_CONFIG.defaultImage
}

/**
 * Utility to truncate description for meta tags
 */
export function truncateDescription(
  description: string,
  maxLength: number = 160,
): string {
  if (description.length <= maxLength) return description
  return description.slice(0, maxLength - 3).trim() + '...'
}
