'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Alert<PERSON><PERSON>gle, RefreshC<PERSON>, Home } from 'lucide-react'

import { ERROR_MESSAGES } from '@/constants'
import { cn } from '@/utils'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showDetails?: boolean
  className?: string
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    }
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    })

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo)
    }

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo)

    // In production, you might want to log to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: logErrorToService(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    })
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <div
          className={cn(
            'flex min-h-[400px] w-full flex-col items-center justify-center p-8 text-center',
            this.props.className,
          )}
        >
          <div className="mb-6 rounded-full bg-red-100 p-4 dark:bg-red-900/20">
            <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>

          <h2 className="mb-4 text-2xl font-semibold text-gray-900 dark:text-gray-100">
            Something went wrong
          </h2>

          <p className="mb-6 max-w-md text-gray-600 dark:text-gray-400">
            {this.state.error?.message || ERROR_MESSAGES.generic}
          </p>

          <div className="flex flex-col gap-3 sm:flex-row">
            <button
              onClick={this.handleRetry}
              className="inline-flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none dark:focus:ring-offset-gray-900"
            >
              <RefreshCw className="h-4 w-4" />
              Try again
            </button>

            <button
              onClick={this.handleGoHome}
              className="inline-flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 dark:focus:ring-offset-gray-900"
            >
              <Home className="h-4 w-4" />
              Go home
            </button>
          </div>

          {/* Error details for development */}
          {this.props.showDetails && process.env.NODE_ENV === 'development' && (
            <details className="mt-8 w-full max-w-2xl">
              <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300">
                Error details (development only)
              </summary>
              <div className="mt-4 rounded-lg bg-gray-100 p-4 text-left dark:bg-gray-800">
                <h4 className="mb-2 font-medium text-red-600 dark:text-red-400">
                  Error:
                </h4>
                <pre className="mb-4 text-xs whitespace-pre-wrap text-gray-800 dark:text-gray-200">
                  {this.state.error?.toString()}
                </pre>

                {this.state.errorInfo && (
                  <>
                    <h4 className="mb-2 font-medium text-red-600 dark:text-red-400">
                      Component Stack:
                    </h4>
                    <pre className="text-xs whitespace-pre-wrap text-gray-800 dark:text-gray-200">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </>
                )}
              </div>
            </details>
          )}
        </div>
      )
    }

    return this.props.children
  }
}

// Hook version for functional components
export function useErrorHandler() {
  return (error: Error, errorInfo?: ErrorInfo) => {
    console.error('Error caught by error handler:', error, errorInfo)

    // In production, you might want to log to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: logErrorToService(error, errorInfo);
    }
  }
}

// Higher-order component for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>,
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`

  return WrappedComponent
}

// Simple error fallback components
export const SimpleErrorFallback = ({ error }: { error?: Error }) => (
  <div className="flex items-center justify-center p-8">
    <div className="text-center">
      <AlertTriangle className="mx-auto mb-4 h-12 w-12 text-red-500" />
      <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-gray-100">
        Something went wrong
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        {error?.message || ERROR_MESSAGES.generic}
      </p>
    </div>
  </div>
)

export const MinimalErrorFallback = () => (
  <div className="flex items-center justify-center p-4">
    <span className="text-sm text-gray-500 dark:text-gray-400">
      Unable to load content
    </span>
  </div>
)

// Async error boundary for handling async errors
export function AsyncErrorBoundary({ children, ...props }: Props) {
  return (
    <ErrorBoundary {...props}>
      <React.Suspense
        fallback={
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
              <p className="text-gray-600 dark:text-gray-400">Loading...</p>
            </div>
          </div>
        }
      >
        {children}
      </React.Suspense>
    </ErrorBoundary>
  )
}
