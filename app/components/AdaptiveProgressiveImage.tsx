'use client'

import { useState, useEffect, useCallback } from 'react'
import { SmartProgressiveImage } from './SmartProgressiveImage'
import { useImagePerformance } from '@/hooks/useImagePerformance'

interface NetworkInformation extends EventTarget {
  effectiveType: '2g' | '3g' | '4g' | 'slow-2g'
  downlink: number
  rtt: number
  saveData: boolean
}

interface AdaptiveProgressiveImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  aspectRatio?: number
  className?: string
  wrapperClassName?: string
  priority?: boolean
  enablePerformanceTracking?: boolean
  onPerformanceUpdate?: (metrics: any) => void
  [key: string]: any
}

/**
 * AdaptiveProgressiveImage component that adjusts loading strategy based on:
 * - Network conditions (connection speed, data saver mode)
 * - Device capabilities (memory, CPU)
 * - User preferences (reduced motion, data saver)
 * - Performance metrics
 */
export function AdaptiveProgressiveImage({
  src,
  alt,
  width = 1200,
  height = 675,
  aspectRatio,
  className,
  wrapperClassName,
  priority = false,
  enablePerformanceTracking = true,
  onPerformanceUpdate,
  ...props
}: AdaptiveProgressiveImageProps) {
  const [networkInfo, setNetworkInfo] = useState<Partial<NetworkInformation>>(
    {},
  )
  const [deviceCapabilities, setDeviceCapabilities] = useState({
    memory: 4, // GB
    cores: 4,
    reducedMotion: false,
    dataSaver: false,
  })
  const [adaptiveSettings, setAdaptiveSettings] = useState({
    enableSmartLQIP: true,
    rootMargin: '100px',
    quality: 90,
    enableIntersectionObserver: true,
    preloadStrategy: 'normal' as 'aggressive' | 'normal' | 'conservative',
  })

  // Performance tracking
  const { metrics, handlers, ref } = useImagePerformance({
    src,
    trackIntersection: true,
    trackCacheHit: true,
    onMetricsUpdate: onPerformanceUpdate,
  })

  // Detect network conditions
  useEffect(() => {
    const updateNetworkInfo = () => {
      const nav = navigator as any
      if ('connection' in nav) {
        const connection = nav.connection as NetworkInformation
        setNetworkInfo({
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData,
        })
      }
    }

    updateNetworkInfo()

    // Listen for network changes
    const nav = navigator as any
    if ('connection' in nav) {
      nav.connection.addEventListener('change', updateNetworkInfo)
      return () =>
        nav.connection.removeEventListener('change', updateNetworkInfo)
    }
  }, [])

  // Detect device capabilities
  useEffect(() => {
    const nav = navigator as any

    setDeviceCapabilities({
      memory: nav.deviceMemory || 4,
      cores: nav.hardwareConcurrency || 4,
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)')
        .matches,
      dataSaver: nav.connection?.saveData || false,
    })

    // Listen for reduced motion changes
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    const handleChange = (e: MediaQueryListEvent) => {
      setDeviceCapabilities((prev) => ({ ...prev, reducedMotion: e.matches }))
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // Adapt settings based on conditions
  useEffect(() => {
    const isSlowNetwork =
      networkInfo.effectiveType === 'slow-2g' ||
      networkInfo.effectiveType === '2g'
    const isMediumNetwork = networkInfo.effectiveType === '3g'
    const isLowMemory = deviceCapabilities.memory < 2
    const isLowCores = deviceCapabilities.cores < 2
    const hasDataSaver = deviceCapabilities.dataSaver || networkInfo.saveData

    let newSettings = {
      enableSmartLQIP: true,
      rootMargin: '100px',
      quality: 90,
      enableIntersectionObserver: true,
      preloadStrategy: 'normal' as 'normal' | 'conservative' | 'aggressive',
    }

    // Adjust for slow networks or data saver mode
    if (isSlowNetwork || hasDataSaver) {
      newSettings = {
        ...newSettings,
        enableSmartLQIP: false, // Skip smart LQIP generation to save bandwidth
        rootMargin: '50px', // Smaller preload margin
        quality: 70, // Lower quality
        preloadStrategy: 'conservative',
      }
    } else if (isMediumNetwork) {
      newSettings = {
        ...newSettings,
        rootMargin: '75px',
        quality: 80,
        preloadStrategy: 'normal',
      }
    } else {
      // Fast network - be more aggressive
      newSettings = {
        ...newSettings,
        rootMargin: '200px', // Larger preload margin
        quality: 90,
        preloadStrategy: 'aggressive',
      }
    }

    // Adjust for low-end devices
    if (isLowMemory || isLowCores) {
      newSettings = {
        ...newSettings,
        enableSmartLQIP: false, // Skip intensive LQIP generation
        rootMargin: '50px',
      }
    }

    // Respect reduced motion preference
    if (deviceCapabilities.reducedMotion) {
      // Could disable animations here if needed
    }

    setAdaptiveSettings(newSettings)
  }, [networkInfo, deviceCapabilities])

  // Combine performance handlers with adaptive logic
  const adaptiveHandlers = {
    onLoadStart: useCallback(() => {
      handlers.onLoadStart()
    }, [handlers]),

    onLoadComplete: useCallback(() => {
      handlers.onLoad()
    }, [handlers]),

    onError: useCallback(
      (error: any) => {
        handlers.onError()
        console.warn('Adaptive image loading error:', error)
      },
      [handlers],
    ),
  }

  // Calculate adaptive aspect ratio
  const calculatedAspectRatio = aspectRatio || width / height

  // Determine preload colors based on network conditions
  const getPreloadColors = () => {
    if (adaptiveSettings.preloadStrategy === 'conservative') {
      // Use simple gray for conservative mode
      return ['#f3f4f6', '#e5e7eb']
    }
    // Let smart LQIP handle color extraction for normal/aggressive modes
    return undefined
  }

  return (
    <div className="adaptive-progressive-image">
      {/* Performance debug info (only in development) */}
      {process.env.NODE_ENV === 'development' && enablePerformanceTracking && (
        <div className="mb-2 rounded bg-gray-100 p-2 text-xs text-gray-500 dark:bg-gray-800">
          <div>
            Network: {networkInfo.effectiveType || 'unknown'} | Memory:{' '}
            {deviceCapabilities.memory}GB
          </div>
          <div>
            Strategy: {adaptiveSettings.preloadStrategy} | Load time:{' '}
            {metrics.loadDuration}ms
          </div>
          <div>
            Cache hit: {metrics.cacheHit ? 'Yes' : 'No'} | Errors:{' '}
            {metrics.errorCount}
          </div>
        </div>
      )}

      <SmartProgressiveImage
        src={src}
        alt={alt}
        width={width}
        height={height}
        aspectRatio={calculatedAspectRatio}
        className={className}
        wrapperClassName={wrapperClassName}
        priority={priority}
        enableSmartLQIP={adaptiveSettings.enableSmartLQIP}
        enableIntersectionObserver={adaptiveSettings.enableIntersectionObserver}
        rootMargin={adaptiveSettings.rootMargin}
        preloadColors={getPreloadColors()}
        quality={adaptiveSettings.quality}
        onLoadStart={adaptiveHandlers.onLoadStart}
        onLoadComplete={adaptiveHandlers.onLoadComplete}
        onError={adaptiveHandlers.onError}
        {...props}
      />
    </div>
  )
}

/**
 * Hook to get current adaptive settings for debugging
 */
export function useAdaptiveImageSettings() {
  const [settings, setSettings] = useState({
    networkType: 'unknown',
    deviceMemory: 4,
    dataSaver: false,
    reducedMotion: false,
    recommendedStrategy: 'normal',
  })

  useEffect(() => {
    const nav = navigator as any
    const connection = nav.connection

    setSettings({
      networkType: connection?.effectiveType || 'unknown',
      deviceMemory: nav.deviceMemory || 4,
      dataSaver: connection?.saveData || false,
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)')
        .matches,
      recommendedStrategy: (() => {
        if (
          connection?.effectiveType === 'slow-2g' ||
          connection?.effectiveType === '2g'
        ) {
          return 'conservative'
        }
        if (connection?.effectiveType === '4g' && nav.deviceMemory >= 4) {
          return 'aggressive'
        }
        return 'normal'
      })(),
    })
  }, [])

  return settings
}
