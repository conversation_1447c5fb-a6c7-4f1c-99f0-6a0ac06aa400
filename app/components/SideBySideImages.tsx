import { IMAGE_CONTEXTS } from '@/lib/imageUtils'

interface SideBySideImagesProps {
  src1: string
  src2: string
  alt1: string
  alt2: string
  isWide?: boolean
  isMid?: boolean
  caption1?: string
  caption2?: string
}

export function SideBySideImages({
  src1,
  src2,
  alt1,
  alt2,
  isWide = false,
  isMid = false,
  caption1 = '',
  caption2 = ''
}: SideBySideImagesProps) {
  // Process image sources
  const processedSrc1 = processMediaSource(src1)
  const processedSrc2 = processMediaSource(src2)

  // Check if these are video files
  const isVideo1 = processedSrc1.match(/\.(mp4|webm|ogg|mov)($|\?)/i)
  const isVideo2 = processedSrc2.match(/\.(mp4|webm|ogg|mov)($|\?)/i)

  // Determine container classes based on the existing CSS in work layout
  const containerClass = isWide
    ? 'wide-image' // Use existing wide-image class for full viewport behavior
    : isMid
      ? 'mid-image' // Use existing mid-image class for 1200px max behavior
      : 'max-w-[1200px] mx-auto' // Default: centered with 1200px max-width

  return (
    <div className={`${containerClass} my-8`}>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          {isVideo1 ? (
            <figure className="m-0">
              <video
                src={processedSrc1}
                autoPlay
                loop
                muted
                playsInline
                className="w-full h-auto rounded-sm transition-all duration-500 ease-out"
                style={{ width: '100%', height: 'auto' }}
              >
                <p>{alt1 || 'Your browser does not support the video tag.'}</p>
              </video>
              {caption1 && (
                <p className="mt-0.5 text-base leading-relaxed text-zinc-700 dark:text-zinc-200">
                  {caption1}
                </p>
              )}
            </figure>
          ) : (
            generateDPIOptimizedImage(processedSrc1, alt1, caption1)
          )}
        </div>
        <div className="flex-1">
          {isVideo2 ? (
            <figure className="m-0">
              <video
                src={processedSrc2}
                autoPlay
                loop
                muted
                playsInline
                className="w-full h-auto rounded-sm transition-all duration-500 ease-out"
                style={{ width: '100%', height: 'auto' }}
              >
                <p>{alt2 || 'Your browser does not support the video tag.'}</p>
              </video>
              {caption2 && (
                <p className="mt-0.5 text-base leading-relaxed text-zinc-700 dark:text-zinc-200">
                  {caption2}
                </p>
              )}
            </figure>
          ) : (
            generateDPIOptimizedImage(processedSrc2, alt2, caption2)
          )}
        </div>
      </div>
    </div>
  )
}

// Helper function to process media source paths
function processMediaSource(src: string): string {
  // If src starts with /work/ change it to /images/work/
  if (src.startsWith('/work/')) {
    return `/images${src}`
  }

  // If src starts with / but not with /images/, add /images prefix
  if (src.startsWith('/') && !src.startsWith('/images/')) {
    return `/images${src}`
  }

  // If src doesn't start with /, prepend /images/
  if (!src.startsWith('/')) {
    return `/images/${src}`
  }

  return src
}

// Generate DPI-optimized image with proper srcset (Intercom style)
function generateDPIOptimizedImage(src: string, alt: string, caption: string): JSX.Element {
  // Get the base path without extension
  const basePath = src.replace(/\.[^.]+$/, '')

  // Use regular context for side-by-side images
  const contextConfig = IMAGE_CONTEXTS.regular
  const contextDPI = contextConfig.dpi

  // Generate DPI-based srcset (Intercom style)
  const srcset = contextDPI
    .map((dpr) => {
      const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
      return `${basePath}${dpiInfo}.avif ${dpr}x`
    })
    .join(', ')

  // Main src uses 1x variant
  const mainSrc = `${basePath}_1x.avif`

  // Generate the optimized image with simple fade-in like about page
  const imgElement = (
    <img
      src={mainSrc}
      srcSet={srcset}
      alt={alt}
      className="w-full h-auto rounded-sm transition-all duration-500 ease-out"
      style={{ width: '100%', height: 'auto' }}
      loading="lazy"
      decoding="async"
    />
  )

  // Wrap in figure if caption exists, otherwise just return img
  if (caption) {
    return (
      <figure className="m-0">
        {imgElement}
        <p className="mt-0.5 text-base leading-relaxed text-zinc-700 dark:text-zinc-200">
          {caption}
        </p>
      </figure>
    )
  } else {
    return imgElement
  }
}


