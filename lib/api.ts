import fs from 'node:fs'
import { join } from 'node:path'
import matter from 'gray-matter'
import { remark } from 'remark'
import html from 'remark-html'
import 'server-only'

export type Post = {
  slug: string
  title: string
  date: string
  content: string
  author?: string
  coverImage?: string
  excerpt?: string
  [key: string]: any
}

const postsDirectory = join(process.cwd(), '_posts')

export function getPostSlugs() {
  return fs.readdirSync(postsDirectory)
}

export function getPostBySlug(
  slug: string,
  fields: string[] = [],
): Partial<Post> {
  const realSlug = slug.replace(/\.md$/, '')
  const fullPath = join(postsDirectory, `${realSlug}.md`)
  const fileContents = fs.readFileSync(fullPath, 'utf8')
  const { data, content } = matter(fileContents)

  const items: Partial<Post> = {}

  // Ensure only the minimal needed data is exposed
  fields.forEach((field) => {
    if (field === 'slug') {
      items[field] = realSlug
    }
    if (field === 'content') {
      items[field] = content
    }

    if (typeof data[field] !== 'undefined') {
      items[field] = data[field]
    }
  })

  return items
}

export function getAllPosts(fields: string[] = []): Partial<Post>[] {
  const slugs = getPostSlugs()
  const posts = slugs
    .map((slug) => getPostBySlug(slug, fields))
    // Sort posts by date in descending order
    .sort((post1, post2) => (post1.date! > post2.date! ? -1 : 1))
  return posts
}

export async function markdownToHtml(markdown: string): Promise<string> {
  const result = await remark().use(html).process(markdown)
  return result.toString()
}

// Synchronous version for static rendering
export function markdownToHtmlSync(markdown: string): string {
  const result = remark().use(html).processSync(markdown)
  return result.toString()
}

// Define the Work type
export type Work = {
  slug: string
  title?: string
  subtitle?: string
  date?: string
  coverImage?: string
  mediaType?: 'image' | 'video'
  mediaUrl?: string
  excerpt?: string
  content?: string
  [key: string]: any
}

// Get all works data
export function getAllWorks(): Work[] {
  const worksDirectory = join(process.cwd(), '_work')

  try {
    const fileNames = fs.readdirSync(worksDirectory)

    const works = fileNames.map((fileName) => {
      // Remove ".md" from file name to get slug
      const slug = fileName.replace(/\.md$/, '')

      // Read markdown file as string
      const fullPath = join(worksDirectory, fileName)
      const fileContents = fs.readFileSync(fullPath, 'utf8')

      // Use gray-matter to parse the post metadata section
      const { data } = matter(fileContents)

      // Combine the data with the slug
      return {
        slug,
        title: data.title,
        subtitle: data.subtitle,
        date: data.date,
        coverImage: data.coverImage,
        mediaType: data.mediaType || 'image',
        mediaUrl: data.mediaUrl || data.coverImage,
        excerpt: data.excerpt,
      } as Work
    })

    // Sort works by date
    return works.sort((a, b) => {
      if (a.date && b.date && a.date < b.date) {
        return 1
      } else {
        return -1
      }
    })
  } catch (error) {
    console.error('Error getting works:', error)
    return []
  }
}

// Get a single work by slug
export function getWorkBySlug(slug: string): Work | null {
  try {
    const fullPath = join(process.cwd(), '_work', `${slug}.md`)
    const fileContents = fs.readFileSync(fullPath, 'utf8')
    const { data, content } = matter(fileContents)

    return {
      slug,
      content,
      title: data.title,
      subtitle: data.subtitle,
      date: data.date,
      coverImage: data.coverImage,
      mediaType: data.mediaType || 'image',
      mediaUrl: data.mediaUrl || data.coverImage,
      excerpt: data.excerpt,
    }
  } catch (error) {
    console.error(`Error getting work by slug ${slug}:`, error)
    return null
  }
}
