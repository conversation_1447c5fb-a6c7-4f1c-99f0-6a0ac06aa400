import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import 'server-only'

const postsDirectory = path.join(process.cwd(), '_posts')

export type Post = {
  slug: string
  title: string
  date: string
  excerpt: string
  author?: string
  coverImage?: string
  content?: string
  visibility?: boolean
}

export function getAllPosts(): Post[] {
  // Get file names under /posts
  const fileNames = fs.readdirSync(postsDirectory)
  const allPostsData = fileNames
    .filter((fileName) => fileName.endsWith('.md'))
    .map((fileName) => {
      // Remove ".md" from file name to get slug
      const slug = fileName.replace(/\.md$/, '')

      // Read markdown file as string
      const fullPath = path.join(postsDirectory, fileName)
      const fileContents = fs.readFileSync(fullPath, 'utf8')

      // Use gray-matter to parse the post metadata section
      const { data, content } = matter(fileContents)

      // Validate required fields
      const title = data.title || ''
      const date = data.date || ''
      const excerpt = data.excerpt || ''
      const visibility = data.visibility !== false // Default to true if not specified

      // Combine the data with the slug
      return {
        slug,
        title,
        date,
        excerpt,
        content,
        visibility,
      }
    })
    .filter((post) => post.visibility !== false) // Filter out posts with visibility: false

  // Sort posts by date
  return allPostsData.sort((a, b) => (a.date < b.date ? 1 : -1))
}

export function getPostBySlug(slug: string): Post | null {
  try {
    // Check if file exists
    const fullPath = path.join(postsDirectory, `${slug}.md`)
    if (!fs.existsSync(fullPath)) {
      return null
    }

    const fileContents = fs.readFileSync(fullPath, 'utf8')
    const { data, content } = matter(fileContents)

    return {
      slug,
      title: data.title || '',
      date: data.date || '',
      excerpt: data.excerpt || '',
      content,
      visibility: data.visibility !== false, // Default to true if not specified
    }
  } catch (error) {
    console.error(`Error getting post with slug ${slug}:`, error)
    return null
  }
}
