import createMDX from '@next/mdx'
import { PHASE_DEVELOPMENT_SERVER } from 'next/constants.js'

/** @type {import('next').NextConfig} */
const nextConfig = (phase) => {
  const isDev = phase === PHASE_DEVELOPMENT_SERVER

  return {
    reactStrictMode: true,
    pageExtensions: ['js', 'jsx', 'ts', 'tsx', 'md', 'mdx'],
    output: 'export',

    // Image optimization configuration (DPI-focused, Intercom style)
    images: {
      unoptimized: false, // Enable optimization in both dev and prod
      // Simplified DPI-focused sizes (base widths for each context)
      deviceSizes: [600, 800, 1200, 1632], // Base widths for contexts
      imageSizes: [320, 480], // Additional smaller sizes
      formats: ['image/avif', 'image/webp'], // Prefer AVIF first, then WebP
      minimumCacheTTL: 31536000, // 1 year cache
      loader: 'custom',
      loaderFile: './app/lib/image-loader.js',
      // Enable DPI-based srcset generation
      dangerouslyAllowSVG: false,
      contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
      remotePatterns: [
        {
          protocol: 'https',
          hostname: '**',
        },
      ],
    },

    // Package transpilation
    transpilePackages: ['motion'],

    // Build optimizations
    experimental: {
      optimizePackageImports: [
        'lucide-react',
        '@fortawesome/react-fontawesome',
      ],
    },

    // Turbopack configuration (stable in Next.js 15)
    turbopack: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },

    // Webpack optimizations
    webpack: (config, { dev, isServer }) => {
      // Optimize bundle splitting
      if (!dev && !isServer) {
        config.optimization.splitChunks = {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks.cacheGroups,
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
            },
          },
        }
      }
      return config
    },

    // Temporarily ignore build errors for refactoring
    eslint: {
      ignoreDuringBuilds: true,
    },
    typescript: {
      ignoreBuildErrors: true,
    },
  }
}

const withMDX = createMDX({
  extension: /\.mdx?$/,
})

export default (phase) => withMDX(nextConfig(phase))
