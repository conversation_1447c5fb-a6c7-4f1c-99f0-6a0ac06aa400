#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const matter = require('gray-matter')

// Simple encryption function that matches the one in the client
function encryptContent(content, password) {
  if (!content || !password) return ''
  let result = ''
  for (let i = 0; i < content.length; i++) {
    // XOR each character with the corresponding character in the password
    const passChar = password.charCodeAt(i % password.length)
    const contentChar = content.charCodeAt(i)
    result += String.fromCharCode(contentChar ^ passChar)
  }
  // Convert to base64 for safe storage
  return Buffer.from(result).toString('base64')
}

// Simple decryption function (same as encryption for XOR)
function decryptContent(encryptedContent, password) {
  if (!encryptedContent || !password) return ''
  try {
    // Decode from base64
    const encrypted = Buffer.from(encryptedContent, 'base64').toString()
    let result = ''
    for (let i = 0; i < encrypted.length; i++) {
      // XOR each character with the corresponding character in the password
      const passChar = password.charCodeAt(i % password.length)
      const encryptedChar = encrypted.charCodeAt(i)
      result += String.fromCharCode(encryptedChar ^ passChar)
    }
    return result
  } catch (error) {
    console.error('Decryption failed:', error)
    return ''
  }
}

// Find all protected work posts and encrypt their content
function encryptProtectedContent() {
  const workDir = path.join(process.cwd(), '_work')

  if (!fs.existsSync(workDir)) {
    console.error('Work directory not found')
    return
  }

  // Get all markdown files
  const files = fs.readdirSync(workDir).filter((file) => file.endsWith('.md'))

  let encryptedCount = 0

  files.forEach((file) => {
    const filePath = path.join(workDir, file)
    const fileContent = fs.readFileSync(filePath, 'utf8')

    // Parse frontmatter
    const { data, content } = matter(fileContent)

    // If this is a password-protected post
    if (data.passwordLock && data.password) {
      // Add the encrypted content to the frontmatter
      data.encryptedContent = encryptContent(content, data.password)

      // Create new file content with updated frontmatter
      const updatedFileContent = matter.stringify(
        data.encryptedContent ? '' : content,
        data,
      )

      // Write back to the file
      fs.writeFileSync(filePath, updatedFileContent)

      console.log(`Encrypted content for: ${file}`)
      encryptedCount++
    }
  })

  console.log(`\nEncryption complete. Encrypted ${encryptedCount} files.`)
}

// Run the encryption
encryptProtectedContent()
