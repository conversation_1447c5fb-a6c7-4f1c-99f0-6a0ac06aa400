const fs = require('fs')
const path = require('path')
const matter = require('gray-matter')

// Default image used when no cover image is specified
const DEFAULT_IMAGE = '/images/thumb_orgvue.jpg'

/**
 * <PERSON>ript to generate static work data from markdown files
 * This runs before the build to create a JSON file with all work data
 */
async function generateWorkData() {
  console.log('🔄 Generating static work data...')
  try {
    const workDirectory = path.join(process.cwd(), '_work')

    // Check if directory exists
    if (!fs.existsSync(workDirectory)) {
      console.warn('Work directory not found:', workDirectory)
      return []
    }

    // Get all markdown files
    const fileNames = fs
      .readdirSync(workDirectory)
      .filter((fileName) => fileName.endsWith('.md'))

    console.log(`📄 Found ${fileNames.length} work markdown files`)

    // Read and parse all work items
    const allWorks = fileNames.map((fileName) => {
      // Remove the .md extension to get the slug
      const slug = fileName.replace(/\.md$/, '')

      // Get the full path to the markdown file
      const fullPath = path.join(workDirectory, fileName)

      // Read the file content
      const fileContents = fs.readFileSync(fullPath, 'utf8')

      // Parse the frontmatter
      const { data } = matter(fileContents)

      // Create the work item
      return {
        slug,
        title: data.title || slug,
        subtitle: data.subtitle || undefined,
        coverImage: data.coverImage || DEFAULT_IMAGE,
        date: data.date
          ? String(data.date)
          : new Date().toISOString().split('T')[0],
        showOnHomepage:
          data.showOnHomepage !== undefined
            ? Boolean(data.showOnHomepage)
            : true,
        passwordLock:
          data.passwordLock !== undefined ? Boolean(data.passwordLock) : false,
        password: data.password || undefined,
        encryptedContent: data.encryptedContent || undefined,
      }
    })

    // Sort by date (newest first)
    const sortedWorks = allWorks.sort((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime()
    })

    // Create the app/data directory if it doesn't exist
    const dataDir = path.join(process.cwd(), 'app', 'data')
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true })
    }

    // Write the data to a JSON file
    const outputFile = path.join(dataDir, 'works-data.json')
    fs.writeFileSync(outputFile, JSON.stringify(sortedWorks, null, 2))

    console.log(`✅ Generated work data saved to: ${outputFile}`)
  } catch (error) {
    console.error('Error generating work data:', error)
  }
}

// Run the function
generateWorkData()
