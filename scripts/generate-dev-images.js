#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const sharp = require('sharp')

/**
 * Generate responsive image variants for development server
 * This ensures that responsive variants are available during development
 * matching the production behavior
 */

// DPI-focused image configurations (Intercom style - matches optimize-images.js)
const IMAGE_CONTEXTS = {
  thumbnail: {
    baseWidth: 800,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 800
  },
  regular: {
    baseWidth: 800,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 800
  },
  mid: {
    baseWidth: 1200,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 1200
  },
  wide: {
    baseWidth: 1632,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 1632
  },
  sideBySide: {
    baseWidth: 600,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 600
  },
  carousel: {
    baseWidth: 800,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 800
  }
}

const FORMATS = ['avif', 'webp', 'original']

const sourceDir = path.join(process.cwd(), 'images')
const targetDir = path.join(process.cwd(), 'public', 'images')

// Ensure target directory exists
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true })
}

// Detect image context from filename and dimensions
function detectImageContext(fileName, width, height) {
  // Detect by filename patterns
  if (fileName.includes('thumb_')) return 'thumbnail'
  if (fileName.includes('carousel')) return 'carousel'

  // Detect by aspect ratio and dimensions
  const aspectRatio = width / height

  // Wide images (typically hero images)
  if (width > 1400 || aspectRatio > 2.5) return 'wide'

  // Medium images
  if (width > 1000 || aspectRatio > 1.8) return 'mid'

  // Side by side images (square or portrait)
  if (aspectRatio <= 1.2) return 'sideBySide'

  // Default to regular
  return 'regular'
}

async function generateResponsiveVariants(inputPath, outputDir, fileName) {
  try {
    const image = sharp(inputPath)
    const metadata = await image.metadata()

    if (!metadata.width || !metadata.height) {
      console.warn(`⚠️ Could not read metadata for ${fileName}`)
      return 0
    }

    // Detect context for this image
    const context = detectImageContext(fileName, metadata.width, metadata.height)
    const contextConfig = IMAGE_CONTEXTS[context]

    const fileExt = path.extname(fileName)
    const baseName = fileName.replace(fileExt, '')

    let variantsGenerated = 0

    for (const format of FORMATS) {
      for (const dpr of contextConfig.dpi) {
        const actualWidth = Math.min(contextConfig.baseWidth * dpr, metadata.width) // Don't upscale
        const quality = contextConfig.quality[dpr]

        // Generate filename with DPI info (Intercom style)
        const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
        const outputFileName = format === 'original'
          ? `${baseName}${dpiInfo}${fileExt}`
          : `${baseName}${dpiInfo}.${format}`

        const outputPath = path.join(outputDir, outputFileName)

        // Skip if this variant already exists and is up to date
        if (fs.existsSync(outputPath)) {
          const srcStats = fs.statSync(inputPath)
          const destStats = fs.statSync(outputPath)
          if (destStats.mtime >= srcStats.mtime) {
            continue
          }
        }

        // Generate the variant
        let pipeline = image.clone().resize(actualWidth, null, {
          withoutEnlargement: true,
          fit: 'inside'
        })

        if (format === 'avif') {
          pipeline = pipeline.avif({ quality })
        } else if (format === 'webp') {
          pipeline = pipeline.webp({ quality })
        } else {
          // Keep original format
          if (metadata.format === 'jpeg') {
            pipeline = pipeline.jpeg({ quality })
          } else if (metadata.format === 'png') {
            pipeline = pipeline.png({ quality: Math.round(quality / 10) })
          }
        }

        await pipeline.toFile(outputPath)
        variantsGenerated++
      }
    }

    return variantsGenerated
  } catch (error) {
    console.error(`Error processing ${fileName}:`, error.message)
    return 0
  }
}

async function copyFile(src, dest) {
  try {
    // Check if source file exists
    if (!fs.existsSync(src)) {
      return false
    }

    // Check if destination already exists and is newer
    if (fs.existsSync(dest)) {
      const srcStats = fs.statSync(src)
      const destStats = fs.statSync(dest)

      // If destination is newer or same age, skip
      if (destStats.mtime >= srcStats.mtime) {
        return false
      }
    }

    // Copy the file
    fs.copyFileSync(src, dest)
    return true
  } catch (error) {
    console.error(`Error copying ${src} to ${dest}:`, error.message)
    return false
  }
}

async function processImagesRecursively(sourceDir, targetDir) {
  let processedCount = 0

  if (!fs.existsSync(sourceDir)) {
    console.log('📂 Source images directory not found:', sourceDir)
    return processedCount
  }

  const items = fs.readdirSync(sourceDir)

  for (const item of items) {
    const sourcePath = path.join(sourceDir, item)
    const targetPath = path.join(targetDir, item)

    const stat = fs.statSync(sourcePath)

    if (stat.isDirectory()) {
      // Recursively process subdirectories
      if (!fs.existsSync(targetPath)) {
        fs.mkdirSync(targetPath, { recursive: true })
      }
      processedCount += await processImagesRecursively(sourcePath, targetPath)
    } else if (stat.isFile()) {
      // Check if it's an image file that needs processing
      const ext = path.extname(item).toLowerCase()
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.avif']
      const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov']

      if (imageExtensions.includes(ext)) {
        // Generate responsive variants for images
        const variantsGenerated = await generateResponsiveVariants(sourcePath, path.dirname(targetPath), item)
        if (variantsGenerated > 0) {
          processedCount++
          console.log(`📸 Generated ${variantsGenerated} variants for ${item}`)
        }
      } else if (videoExtensions.includes(ext)) {
        // Just copy video files
        if (await copyFile(sourcePath, targetPath)) {
          processedCount++
          console.log(`🎥 Copied video ${item}`)
        }
      }
    }
  }

  return processedCount
}

async function main() {
  console.log('📋 Generating responsive image variants for development...')
  const processedCount = await processImagesRecursively(sourceDir, targetDir)

  if (processedCount > 0) {
    console.log(`✅ Processed ${processedCount} images/videos for development`)
  } else {
    console.log('⏭️ All images are up to date')
  }
}

main().catch(console.error)
