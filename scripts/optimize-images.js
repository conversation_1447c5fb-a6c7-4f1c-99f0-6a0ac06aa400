#!/usr/bin/env node

/**
 * Enhanced multi-threaded image optimization and video copying script
 *
 * This script:
 * 1. Reads original images and videos from /images, as well as media referenced in _posts and _work
 * 2. Optimizes images using sharp with multi-threading and parallel processing
 * 3. Copies video files directly (no optimization needed)
 * 4. Saves them to /public/images with the same directory structure
 * 5. Uses smart change detection based on file modification times (no cache files)
 * 6. Supports progressive image loading with LQIP generation
 */

const fs = require('fs').promises
const fsSync = require('fs')
const path = require('path')
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads')
const os = require('os')
const sharp = require('sharp')

// Worker pool configuration
const MAX_WORKERS = Math.min(os.cpus().length, 8) // Limit to 8 workers max
const workers = []
let workerIndex = 0

/**
 * Fast change detection using file modification times
 * Much faster than content hashing as it doesn't require reading file contents
 */
function needsProcessing(sourcePath, outputPaths) {
  try {
    // Get source file stats
    const sourceStats = fsSync.statSync(sourcePath)
    const sourceMtime = sourceStats.mtime.getTime()
    const sourceSize = sourceStats.size

    // Check if any output file is missing or older than source
    for (const outputPath of outputPaths) {
      if (!fsSync.existsSync(outputPath)) {
        return true
      }

      const outputStats = fsSync.statSync(outputPath)
      const outputMtime = outputStats.mtime.getTime()

      // If source is newer than output, needs processing
      if (sourceMtime > outputMtime) {
        return true
      }
    }

    return false
  } catch (error) {
    return true
  }
}

// Enhanced Configuration
const INPUT_DIRS = [
  path.join(process.cwd(), 'images'),
  path.join(process.cwd(), '_posts'),
  path.join(process.cwd(), '_work'),
]
const OUTPUT_DIR = path.join(process.cwd(), 'public', 'images')
const FORMATS = ['webp', 'avif', 'original'] // Added AVIF for better compression

// File type configurations
const IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.avif', '.gif']
const VIDEO_EXTENSIONS = ['.mp4', '.webm', '.ogg', '.mov']

/**
 * Worker thread for image processing
 */
if (!isMainThread) {
  // Worker thread code
  const { imagePath, outputVariants, contextConfig } = workerData

  async function processImageInWorker() {
    try {
      const image = sharp(imagePath)
      const metadata = await image.metadata()
      const results = []

      for (const variant of outputVariants) {
        const { format, size, dpr, quality, outputPath } = variant
        const actualWidth = Math.min(size * dpr, metadata.width)

        if (format === 'original') {
          const fileExt = path.extname(imagePath).toLowerCase()
          await image
            .clone()
            .resize({ width: actualWidth, withoutEnlargement: true })
            .jpeg({
              quality: quality,
              progressive: true,
              force: fileExt === '.jpg' || fileExt === '.jpeg',
            })
            .png({
              compressionLevel: 9,
              progressive: true,
              force: fileExt === '.png',
            })
            .webp({ quality: quality, force: fileExt === '.webp' })
            .toFile(outputPath)
        } else {
          await image
            .clone()
            .resize({ width: actualWidth, withoutEnlargement: true })
            .toFormat(format, { quality: quality })
            .toFile(outputPath)
        }

        results.push({
          outputPath,
          actualWidth,
          dpr,
          quality,
          format
        })
      }

      parentPort.postMessage({ success: true, results })
    } catch (error) {
      parentPort.postMessage({ success: false, error: error.message })
    }
  }

  processImageInWorker()
  return
}

// DPI-focused image processing configuration (Intercom style)
const IMAGE_CONTEXTS = {
  // Work thumbnails - aspect-video, responsive grid
  thumbnail: {
    baseWidth: 800, // Single base width for context
    dpi: [1, 2], // 1x, 2x variants only (no 3x)
    quality: { 1: 90, 2: 85 }, // Slightly higher quality for 2x
    aspectRatio: 16/9,
    maxWidth: 800
  },

  // Work post regular images - max 800px
  regular: {
    baseWidth: 800,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 800
  },

  // Work post mid images - max 1200px
  mid: {
    baseWidth: 1200,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 1200
  },

  // Work post wide images - max 1632px
  wide: {
    baseWidth: 1632,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 1632
  },

  // Side-by-side images - 600px base
  sideBySide: {
    baseWidth: 600,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 600
  },

  // Carousel images - 800x600 base
  carousel: {
    baseWidth: 800,
    dpi: [1, 2],
    quality: { 1: 90, 2: 85 },
    maxWidth: 800
  }
}

// Auto-detect context based on image usage patterns
function detectImageContext(filePath, metadata) {
  const fileName = path.basename(filePath, path.extname(filePath))
  const { width, height } = metadata

  // Detect by filename patterns
  if (fileName.includes('thumb_')) return 'thumbnail'
  if (fileName.includes('carousel') || fileName.includes('_carousel_')) return 'carousel'

  // Detect by aspect ratio and dimensions
  const aspectRatio = width / height

  // Thumbnail detection (16:9 aspect ratio, medium size)
  if (Math.abs(aspectRatio - (16/9)) < 0.1 && width >= 800 && width <= 2000) {
    return 'thumbnail'
  }

  // Wide image detection (very wide or large images)
  if (width > 1400 || aspectRatio > 2) {
    return 'wide'
  }

  // Mid image detection (medium-large images)
  if (width > 1000 && width <= 1400) {
    return 'mid'
  }

  // Small images likely for side-by-side
  if (width <= 800) {
    return 'sideBySide'
  }

  // Default to regular for everything else
  return 'regular'
}



// Create output directory if it doesn't exist
if (!fsSync.existsSync(OUTPUT_DIR)) {
  fsSync.mkdirSync(OUTPUT_DIR, { recursive: true })
}

// Set to track processed images (avoid duplicates)
const processedImages = new Set()

// Set to track image paths extracted from markdown
const extractedImagePaths = new Set()

// Enhanced statistics counters
const stats = {
  imagesProcessed: 0,
  imagesSkipped: 0,
  videosProcessed: 0,
  videosSkipped: 0,
  responsiveVariantsCreated: 0,
  totalSizeSaved: 0,
}

/**
 * Worker pool management
 */
class WorkerPool {
  constructor(maxWorkers = MAX_WORKERS) {
    this.maxWorkers = maxWorkers
    this.workers = []
    this.queue = []
    this.activeJobs = 0
  }

  async processImage(imagePath, outputVariants, contextConfig) {
    return new Promise((resolve, reject) => {
      this.queue.push({ imagePath, outputVariants, contextConfig, resolve, reject })
      this.processQueue()
    })
  }

  processQueue() {
    if (this.queue.length === 0 || this.activeJobs >= this.maxWorkers) {
      return
    }

    const job = this.queue.shift()
    this.activeJobs++

    const worker = new Worker(__filename, {
      workerData: {
        imagePath: job.imagePath,
        outputVariants: job.outputVariants,
        contextConfig: job.contextConfig
      }
    })

    worker.on('message', (result) => {
      this.activeJobs--
      worker.terminate()

      if (result.success) {
        job.resolve(result.results)
      } else {
        job.reject(new Error(result.error))
      }

      this.processQueue()
    })

    worker.on('error', (error) => {
      this.activeJobs--
      worker.terminate()
      job.reject(error)
      this.processQueue()
    })
  }

  async waitForCompletion() {
    while (this.activeJobs > 0 || this.queue.length > 0) {
      await new Promise(resolve => setTimeout(resolve, 10))
    }
  }
}

const workerPool = new WorkerPool()

/**
 * Extract image paths from markdown content
 * @param {string} content - Markdown content
 * @returns {string[]} - Array of image paths
 */
function extractImagesFromMarkdown(content) {
  const imagePaths = []

  // Extract from frontmatter coverImage
  const coverImageMatch = content.match(/coverImage:\s*["']?([^"'\s]+)["']?/)
  if (coverImageMatch && coverImageMatch[1]) {
    const coverImage = coverImageMatch[1]
    if (!coverImage.startsWith('http')) {
      imagePaths.push(coverImage)
    }
  }

  // Extract from markdown image syntax: ![alt](/path/to/image.jpg)
  const markdownImageRegex = /!\[.*?\]\(([^)]+)\)/g
  let match
  while ((match = markdownImageRegex.exec(content)) !== null) {
    const imagePath = match[1].split(' ')[0] // Handle cases with title: ![alt](/path/to/image.jpg "title")
    if (!imagePath.startsWith('http')) {
      imagePaths.push(imagePath)
    }
  }

  // Extract from HTML img tags: <img src="/path/to/image.jpg" />
  const htmlImageRegex = /<img.*?src=["']([^"']+)["']/g
  while ((match = htmlImageRegex.exec(content)) !== null) {
    const imagePath = match[1]
    if (!imagePath.startsWith('http')) {
      imagePaths.push(imagePath)
    }
  }

  return imagePaths
}

/**
 * Process a markdown file to extract image paths
 * @param {string} filePath - Path to the markdown file
 */
function processMarkdownFile(filePath) {
  try {
    const content = fsSync.readFileSync(filePath, 'utf8')
    const imagePaths = extractImagesFromMarkdown(content)

    for (let imagePath of imagePaths) {
      // Normalize path to start from /images
      if (imagePath.startsWith('/')) {
        if (!imagePath.startsWith('/images/')) {
          imagePath = `/images${imagePath}`
        }
      } else {
        imagePath = `/images/${imagePath}`
      }

      // Add to extraction set
      extractedImagePaths.add(imagePath)
    }

    if (imagePaths.length > 0) {
      console.log(
        `📄 Found ${imagePaths.length} images in ${path.relative(process.cwd(), filePath)}`,
      )
    }
  } catch (error) {
    console.error(
      `❌ Error processing markdown file ${filePath}:`,
      error.message,
    )
  }
}

/**
 * Copy a video file to the output directory
 * @param {string} filePath - Path to the video file
 * @param {string} baseDir - Base directory for calculating relative path
 */
async function copyVideo(filePath, baseDir) {
  // Calculate output paths
  const relativePath = path.relative(baseDir, filePath)
  const fileName = path.basename(filePath)
  const dirName = path.dirname(relativePath)
  const outputDir = path.join(OUTPUT_DIR, dirName)
  const outputPath = path.join(outputDir, fileName)

  // Generate a unique key for this video to avoid processing duplicates
  const videoKey = path.join(dirName, fileName).replace(/\\/g, '/')

  // Skip if already processed in this run
  if (processedImages.has(videoKey)) {
    return
  }

  // Check if video needs to be copied (new or modified)
  if (!needsProcessing(filePath, [outputPath])) {
    console.log(`⏭️ Skipped video (up to date): ${relativePath}`)
    processedImages.add(videoKey)
    stats.videosSkipped++
    return
  }

  // Create output directory if it doesn't exist
  if (!fsSync.existsSync(outputDir)) {
    await fs.mkdir(outputDir, { recursive: true })
  }

  try {
    // Copy the video file directly (no optimization)
    await fs.copyFile(filePath, outputPath)
    console.log(
      `📹 Copied video: ${relativePath} -> ${path.relative(process.cwd(), outputPath)}`,
    )

    // Mark as processed
    processedImages.add(videoKey)
    stats.videosProcessed++
  } catch (error) {
    console.error(`❌ Error copying video ${relativePath}:`, error.message)
  }
}

/**
 * Process an image file using worker threads for parallel processing
 * @param {string} filePath - Path to the image file
 * @param {string} baseDir - Base directory for calculating relative path
 */
async function processImage(filePath, baseDir) {
  // Calculate output paths
  const relativePath = path.relative(baseDir, filePath)
  const fileName = path.basename(filePath)
  const dirName = path.dirname(relativePath)
  const outputDir = path.join(OUTPUT_DIR, dirName)
  const fileExt = path.extname(filePath).toLowerCase()

  // Generate a unique key for this image to avoid processing duplicates
  const imageKey = path.join(dirName, fileName).replace(/\\/g, '/')

  // Skip if already processed in this run
  if (processedImages.has(imageKey)) {
    return
  }

  // Handle video files
  if (VIDEO_EXTENSIONS.includes(fileExt)) {
    await copyVideo(filePath, baseDir)
    return
  }

  // Only process image files
  if (!IMAGE_EXTENSIONS.includes(fileExt)) {
    return
  }

  // Get image metadata for context detection
  let metadata
  try {
    const image = sharp(filePath)
    metadata = await image.metadata()
  } catch (error) {
    console.error(`❌ Error reading metadata for ${relativePath}:`, error.message)
    return
  }

  // Detect image context for smart responsive generation
  const context = detectImageContext(filePath, metadata)
  const contextConfig = IMAGE_CONTEXTS[context]

  console.log(`🔍 Detected context: ${context} for ${relativePath}`)

  // Create output directory if it doesn't exist
  if (!fsSync.existsSync(outputDir)) {
    await fs.mkdir(outputDir, { recursive: true })
  }

  // Prepare variants to generate
  const outputVariants = []
  const allOutputPaths = []

  for (const format of FORMATS) {
    for (const dpr of contextConfig.dpi) {
      const actualWidth = Math.min(contextConfig.baseWidth * dpr, metadata.width) // Don't upscale
      const quality = contextConfig.quality[dpr]

      // Generate filename with DPI info (Intercom style)
      const dpiInfo = dpr > 1 ? `_${dpr}x` : `_1x`
      const outputFileName = format === 'original'
        ? fileName.replace(fileExt, `${dpiInfo}${fileExt}`)
        : fileName.replace(fileExt, `${dpiInfo}.${format}`)

      const outputPath = path.join(outputDir, outputFileName)
      allOutputPaths.push(outputPath)

      // Skip if this variant already exists and is up to date
      if (!needsProcessing(filePath, [outputPath])) {
        continue
      }

      outputVariants.push({
        format,
        size: contextConfig.baseWidth,
        dpr,
        quality,
        outputPath,
        actualWidth
      })
    }
  }

  // Check if any variants need processing
  if (outputVariants.length === 0) {
    console.log(`⏭️ Skipped image (up to date): ${relativePath}`)
    processedImages.add(imageKey)
    stats.imagesSkipped++
    return
  }

  try {
    // Process image variants using worker thread
    const results = await workerPool.processImage(filePath, outputVariants, contextConfig)

    // Log results
    for (const result of results) {
      console.log(
        `✅ Generated: ${relativePath} -> ${path.relative(process.cwd(), result.outputPath)} (${result.actualWidth}px, ${result.dpr}x DPR, Q${result.quality})`,
      )
      stats.responsiveVariantsCreated++
    }

    // Mark as processed
    processedImages.add(imageKey)
    stats.imagesProcessed++
  } catch (error) {
    console.error(`❌ Error processing ${relativePath}:`, error.message)
  }
}

/**
 * Walk through a directory and process all files with parallel processing
 * @param {string} dir - Directory to walk
 * @param {string} baseDir - Base directory for calculating relative paths
 * @param {boolean} isMarkdownDir - Whether this directory contains markdown files
 */
async function processDirectory(dir, baseDir, isMarkdownDir = false) {
  let entries
  try {
    entries = await fs.readdir(dir, { withFileTypes: true })
  } catch (error) {
    console.error(`❌ Error reading directory ${dir}:`, error.message)
    return
  }

  // Process directories first (sequential to avoid too many concurrent operations)
  for (const entry of entries) {
    if (entry.isDirectory()) {
      const fullPath = path.join(dir, entry.name)
      await processDirectory(fullPath, baseDir, isMarkdownDir)
    }
  }

  // Collect all files to process
  const filesToProcess = []
  for (const entry of entries) {
    if (!entry.isDirectory()) {
      const fullPath = path.join(dir, entry.name)

      if (isMarkdownDir && (entry.name.endsWith('.md') || entry.name.endsWith('.mdx'))) {
        // Process markdown files synchronously (they're fast)
        processMarkdownFile(fullPath)
      } else if (!isMarkdownDir) {
        const fileExt = path.extname(entry.name).toLowerCase()
        if (IMAGE_EXTENSIONS.includes(fileExt) || VIDEO_EXTENSIONS.includes(fileExt)) {
          filesToProcess.push(fullPath)
        }
      }
    }
  }

  // Process image/video files in parallel batches
  const BATCH_SIZE = MAX_WORKERS * 2 // Process in batches to avoid overwhelming the system
  for (let i = 0; i < filesToProcess.length; i += BATCH_SIZE) {
    const batch = filesToProcess.slice(i, i + BATCH_SIZE)
    const promises = batch.map(filePath => processImage(filePath, baseDir))
    await Promise.all(promises)
  }
}

/**
 * Process images and videos referenced in markdown but located outside the input directories
 */
async function processExtractedImages() {
  console.log(
    '🔍 Processing images and videos referenced in markdown content...',
  )

  const filesToProcess = []

  for (const imagePath of extractedImagePaths) {
    // Convert from web path to filesystem path
    let normalizedPath = imagePath
    if (normalizedPath.startsWith('/images/')) {
      normalizedPath = normalizedPath.substring('/images/'.length)
    } else if (normalizedPath.startsWith('/')) {
      normalizedPath = normalizedPath.substring(1)
    }

    // Check if image/video exists in images directory
    const absolutePath = path.join(process.cwd(), 'images', normalizedPath)
    if (fsSync.existsSync(absolutePath)) {
      filesToProcess.push(absolutePath)
    } else {
      console.warn(
        `⚠️ Referenced image/video not found: ${imagePath} (${absolutePath})`,
      )
    }
  }

  // Process files in parallel batches
  const BATCH_SIZE = MAX_WORKERS * 2
  for (let i = 0; i < filesToProcess.length; i += BATCH_SIZE) {
    const batch = filesToProcess.slice(i, i + BATCH_SIZE)
    const promises = batch.map(filePath =>
      processImage(filePath, path.join(process.cwd(), 'images'))
    )
    await Promise.all(promises)
  }
}



// Start processing
;(async function main() {
  const startTime = Date.now()
  console.log('🖼️ Starting fast multi-threaded image processing...')
  console.log(`🔧 Using ${MAX_WORKERS} worker threads for parallel processing`)

  try {
    // Step 1: Process markdown files to extract image and video references
    console.log('📝 Scanning markdown files for image and video references...')
    for (const inputDir of INPUT_DIRS) {
      if (inputDir.includes('_posts') || inputDir.includes('_work')) {
        console.log(`📂 Scanning for markdown in: ${inputDir}`)
        await processDirectory(inputDir, inputDir, true)
      }
    }

    // Step 2: Process images and videos from media directories
    console.log('🔍 Processing image and video files...')
    for (const inputDir of INPUT_DIRS) {
      if (!inputDir.includes('_posts') && !inputDir.includes('_work')) {
        console.log(`📂 Processing images and videos from: ${inputDir}`)
        await processDirectory(inputDir, inputDir, false)
      }
    }

    // Step 3: Process any extracted images/videos that weren't found in the regular processing
    await processExtractedImages()

    // Step 4: Wait for all worker threads to complete
    console.log('⏳ Waiting for all processing to complete...')
    await workerPool.waitForCompletion()

    // Display statistics
    const endTime = Date.now()
    const duration = ((endTime - startTime) / 1000).toFixed(2)

    console.log('\n📊 Processing Summary:')
    console.log(`   ⏱️  Total time: ${duration}s`)
    console.log(`   🖼️  Images: ${stats.imagesProcessed} processed, ${stats.imagesSkipped} skipped (up to date)`)
    console.log(`   📹 Videos: ${stats.videosProcessed} processed, ${stats.videosSkipped} skipped (up to date)`)
    console.log(`   🎨 Variants: ${stats.responsiveVariantsCreated} responsive variants created`)
    console.log(`   📊 Total: ${stats.imagesProcessed + stats.videosProcessed} processed, ${stats.imagesSkipped + stats.videosSkipped} skipped`)

    if (stats.imagesProcessed > 0 || stats.videosProcessed > 0) {
      const avgTimePerFile = (duration / (stats.imagesProcessed + stats.videosProcessed)).toFixed(2)
      console.log(`   ⚡ Average: ${avgTimePerFile}s per file`)
    }

    console.log('✨ Fast image processing completed!')
  } catch (error) {
    console.error('❌ An error occurred:', error)
    process.exit(1)
  }
})().catch((error) => {
  console.error('❌ An error occurred:', error)
  process.exit(1)
})
