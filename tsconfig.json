{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,

    // Additional strict checks (temporarily relaxed for migration)
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "exactOptionalPropertyTypes": false,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": false,

    // Import/export settings
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,

    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["./app/components/*", "./components/*"],
      "@/lib/*": ["./app/lib/*", "./lib/*"],
      "@/types/*": ["./app/types/*", "./types/*"],
      "@/hooks/*": ["./app/hooks/*", "./hooks/*"],
      "@/utils/*": ["./app/utils/*", "./utils/*"],
      "@/constants/*": ["./app/constants/*"],
      "@/constants": ["./app/constants/index.ts"],
      "@/utils": ["./app/utils/index.ts"],
      "@/types": ["./app/types/index.ts"],
      "@/hooks": ["./app/hooks/index.ts"]
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "next.config.mjs"
  ],
  "exclude": ["node_modules", ".next", "out", "public"]
}
