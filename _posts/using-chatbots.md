---
title: 'Using chatbots as a habit'
date: '2025-02-08'
excerpt: 'My current system to integrate chatbot to my daily work'
visibility: true
---

![Cover (wide)](/images/blog_chatbots.jpg)

# Optimizing Your AI Chatbot Workflow

AI chatbots like ChatGPT and <PERSON> have become indispensable tools. You can access them via browser, phone, or integrations like Mac shortcuts. But there's no obvious winner yet. Tools improve rapidly—today's favorite might fall behind tomorrow, or each might excel in different ways. So how can you navigate this easily?

&nbsp;

# My Current Solution

After experimenting, I've settled on using a dedicated browser window—ideally an entire browser like Vivaldi—just for AI chatbots. Clicking one icon instantly opens my AI workspace without distractions.

Here's how mine looks: small icons on the left for quick switching between chatbots, with <PERSON><PERSON><PERSON> fully customized for optimal workflow. This customization is <PERSON><PERSON><PERSON>'s standout advantage—far more flexible than typical browsers.

![Icons](/images/blog_chatbots_1.jpg)

&nbsp;

# Reducing Typing Friction

One persistent friction in chatbot interactions is typing long prompts. Speaking is simply faster, so I've started using <PERSON>'s built-in speech-to-text. It dramatically streamlines my workflow.

![Text to speech](/images/blog_chatbots_2.jpg)

&nbsp;

# An Evolving Process

My setup keeps evolving. If you've found a better method, I'd love to hear about it.
