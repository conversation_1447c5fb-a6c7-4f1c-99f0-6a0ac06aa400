---
title: "Optimising working with computers"
date: "2025-02-01"
excerpt: "For projects big and small"
visibility: false
---

![Cover (wide)](/images/blog_chatbots.jpg)

- In our field, everyone works with a computer.
- And since we work with it everyday, performing some actions many times day
- trying to opitimise the way we work with it would be quite beneficial

So starting with the physical interfaces.
The computer, monitor, mouse and keyboard

Computer
I've noticed significant speed improvement with M Series chip, for both interacting with and opening apps.

monitor
I am currently using 3 monitors
But I am currently thinking maybe 2 is the most optimal.
The ideal size is 27-32 inch, 4K
For design related task go for the ones with good color accuracy, I go for Eizo for its good color uniformity. Monitors are not build the same, a lot of monitors like some of the Asus proarts dont have good color uniformity, which means you will see the left of the monitor to be greener, which could drive a design crazy.
Ideally there is also ultra low latency and high reference rate, but other than OLED that could achieve that at the moment, the current price and the burn in issue might make it less attractive for now.

Mouse
Always pick on that is low latency, high dpi and light, you will be suprise how much better that is. I come from an MX Master 3 to a Keychron M5, it is just much more snappy. Saving 2ms every time and overtime it adds up quite alot.

keyboard
I got a mechanical keyboard, but the lastest release of annologe keyboards seems to be promissing with low latency and ability to set trigger hieght.

However, I'm finding myself using speech to text more and more because it's simply faster and that could be the future of inputting text. I'm currently using the one that's built to macOS which I could trigger with clicking control twice.


On the software side, I have a A couple of habits
- Use text speech for long paragraphs or readings
- You speech to text for writing
- Use aerospace window manager To organize and navigate between windows faster
- He dedicated browser customized for AI




&nbsp;