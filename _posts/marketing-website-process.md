---
title: 'The evolution of the marketing website design process'
date: '2025-02-04'
excerpt: 'For projects big and small'
visibility: false
---

![Cover (wide)](/images/blog_chatbots.jpg)

# Introduction

I hope there are no shortage of high budget projects. We are seeing the general market trending downwards.

&nbsp;

When I started at dusted there is no formal UX process, Designers at the branding team usually handles the page design, which cause a lot of problems.

So how we design your process is to first have a full process with every conceivable things that we could do for the clients and then based on specific client requirements remove or simplify as needed.

Below is a demo of the fool IAX process in a reduced version of \*

# evolution

you've made a lot of change to the process while we apply it to client work, Some of which are below:

## Research

- Put all client documents into NAI workspace so that we could Query if necessary (best at this task at the time is Google AI studio), Expecting a huge improvement on tools for this task to main technology I'll be looking for is an improved contexts window. We experimented with using local rag which uses factor databases to store information however we find that it hallucinate to a level that is unacceptable.

- Use tools like eagle to curate a collection of webpages (competitors, adjasunt markets). Clients are often interested in seeing how their competitors do. Things are solve a problem and it's interested in seeing their site side-by-side. At the time, we are also experimenting with an automated way to do this with our Proprietary platform.

- Proto persona generation based on client documents and requirements

## IA / Wireframes

- Use wireframed interactive top navigation prototype along with IA, You find that IAS on its own it's too much abstraction, Having an interactive protype which stakeholders could play with, helps with the understanding.

- For clients that are easily distracted by wireframes in ultra low wireframes

- Generate filler contents at the wire frame stage for clients who could not visualize very well.

## General

- Use Figma, Figma Slides and Figjam to Avoid the tool switching tax. Copy and pasting within the Felmet ecosystem saves it Norris amount of time I could not be underestimated.

- And alternative approach would be components focused, The objective is to quickly identify a set of components that is needed and optimize each components and its variance so that marketing teams could flexibly change and assemble pages according to their needs. While suggesting a best initial approach for Paige components sequencing.

&nbsp;

# Future

In the product space we have seen PMs created prototypes / MVPs with tools like raplit.
It may be possible that in the near future from design to deployment, it could be a week.

However, currently is a car it's a process of constantly testing you tools to see if it works because very often it doesn't.

Here are some of the things we tried, but with current technology, it doesn't work yet:

- To upload an image to AI and have it audited the webpage for usability issues
-
