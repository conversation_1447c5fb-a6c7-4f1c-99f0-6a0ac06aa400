---
title: 'Building with Cursor + Next.js'
date: '2025-04-22'
excerpt: 'What I learn from creating my portfolio website with Cursor agent mode'
visibility: true
---

![Image 2](/images/blog_cursor.png)

Back in the day, I would rebuild my portfolio website from time to time just for fun. Using SSGs like Hugo and 11<PERSON>, I would base it off a template, add the features I wanted, and customize it to my liking. However, back then, I usually had to read through the documentation or watch videos to understand how the framework worked, and when I implemented features, I would have to read through example code. And when I encountered errors, I would have to solve it myself or search Stack Overflow.

But now with Cursor + Claude 3.7 in agent mode, it is a different world.

&nbsp;

# Here's what I learned, distilled to save you time:

## 1. Start with Next.js and a template.

I initially tried Astro and hit endless cascading issues. The AI agent attempted fixes but got trapped in solution loops until I researched and directed it specifically.

Next.js provided a smoother experience, likely because its popularity gives LLMs more training material to draw from. The AI coding community seems to prioritize Next.js optimization. Use a template that includes complex functionality you'll need.

&nbsp;

## 2. Types of task the agent performances well at

2.1 **Add explanatory comments throughout your codebase:** even the smaller 4o model handles this well.

```javascript
Please comment above each line of code and explain waht it does
```

2.2 **Debugging issues:** Works for alot of problems

```javascript
Currently on the blog post page, when I use </br> in the .md,
there is no line break or spacing, please fix
```

2.3 **Add, modify, and remove components:** a massive time-saver. It can even implement layouts from design screenshots, though results improve when you specify details like animation libraries.

```javascript
Please add the testimonial section at the bottom of the About page,
please refer to the image for the design
```

&nbsp;

## 3. How to prevent it from going "freestyle"

Be explicit about your framework: Even with this guidance, expect to try a few iterations before getting exactly what you want.

```javascript
Remember this is a next.js 15.3.0 fully static site
```

&nbsp;

## 4. Currently it still require some understanding of the technology used

It is more likely to produce the correct results if you understand some of the technical basics, such as image optimisation, animation libraries (motion), CSS & JS syntax.

&nbsp;

# Future

We're witnessing a shift from chatbots to agents, and from my initial experience, it's a game-changer. I know someone who generated an e-commerce website in just three days using Manus AI for the initial code generation and then used Cursor to further modify and debug.

This could mean many things, but one significant outcome will be the ability to create world-class products with much smaller teams than before.
