# Image Optimization Workflow

This document explains the image workflow for this Next.js 15.3.0 static site.

## Overview

The image workflow consists of the following steps:

1. Original images are stored in the `/images` directory
2. Image references can be used in both `/images`, `/_posts`, and `/_work` markdown files
3. During development, these original images are served directly
4. For production builds, images are optimized and stored in `/public/images`
5. In production, these optimized images are used in the deployed site (Cloudflare Pages)

## How It Works

### Development Mode

In development mode:

- Original images from `/images` are used directly
- Next.js Image component settings in `next.config.js` allow for on-the-fly optimization
- Paths are automatically resolved to the correct location

### Production Builds

For production:

1. Original images remain in `/images` (these aren't deployed)
2. The `optimize-images` script:
   - Processes images in the `/images` directory
   - Scans markdown files in `/_posts` and `/_work` for image references
   - Optimizes all discovered images (resizing, compression)
   - Creates WebP versions of all images
   - Saves results to `/public/images` with the same directory structure
3. The Next.js Image component is configured to use the optimized images
4. All optimized images are included in the build output
5. Cloudflare Pages serves these optimized static assets

## Usage

### Adding New Images

1. Place your original images in the `/images` directory:

   ```
   /images/
     work/
       project-a.jpg
       project-b.png
     blog/
       article-image.jpg
   ```

2. Reference images in your components using the Next.js Image component:

   ```jsx
   import Image from 'next/image'

   export function MyComponent() {
     return (
       <Image
         src="/work/project-a.jpg"
         alt="Project A"
         width={1200}
         height={675}
       />
     )
   }
   ```

3. Or reference images in your markdown content:

   ```md
   ---
   title: My Blog Post
   coverImage: /blog/article-image.jpg
   ---

   # My Blog Post

   ![An example image](/blog/article-image.jpg)
   ```

4. The path will be automatically resolved to the correct location:
   - In development: `/images/work/project-a.jpg`
   - In production: `/images/work/project-a.webp` (optimized version)

### Image Locations

You can reference images in several ways:

1. **In React components**: Use the Next.js Image component with relative paths
2. **In Markdown frontmatter**: Use the `coverImage` field with an absolute path
3. **In Markdown content**: Use standard markdown image syntax `![alt](/path/to/image.jpg)`

The image optimization script will find all these references and process the corresponding images.

### Manually Optimizing Images

Run the following command to optimize images:

```bash
npm run img
```

This will:

1. Scan markdown files in `/_posts` and `/_work` for image references
2. Read all images from `/images` and the referenced paths
3. Optimize and resize them
4. Save the optimized versions to `/public/images`

### Automatic Optimization During Build

When you run `npm run build`, the `postbuild` script automatically calls the image optimization script, ensuring that all images in the `/images` directory and those referenced in markdown files are optimized before deployment.

## Important Notes

- Always keep original, high-quality images in the `/images` directory
- Don't manually modify files in `/public/images` - they will be overwritten
- Remote images (starting with http://) are not processed by the optimization script
- If you need to change optimization settings, modify the `scripts/optimize-images.js` file
- For best performance in production, images are converted to WebP format when possible

## Troubleshooting

If images aren't displaying correctly:

1. Check that the path is correct relative to the `/images` directory
2. Verify that the image optimization script ran successfully
3. For local development, restart the development server
4. Ensure the image exists in the correct location
5. Check the console output of the optimization script for any warnings about missing images
