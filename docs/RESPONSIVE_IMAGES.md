# Context-Aware Responsive Image System

This document describes the enhanced responsive image system that automatically detects image usage contexts and generates optimized variants for hi-DPI screens and different viewport sizes.

## Overview

The system analyzes how images are actually used on your site and generates context-specific responsive variants, ensuring optimal performance and visual quality across all devices and screen densities.

## Key Features

### 🎯 **Context-Aware Detection**
- Automatically detects image usage patterns (thumbnails, content images, wide images, etc.)
- Generates appropriate responsive sizes for each context
- Optimizes for actual display dimensions rather than generic breakpoints

### 📱 **Hi-DPI Optimization**
- Generates 1x, 2x, and 3x variants for retina displays
- Uses variable quality (lower quality for higher DPR to reduce file size)
- Automatic density descriptor srcsets for crisp images on all screens

### 🚀 **Performance Optimized**
- AVIF → WebP → Original format fallback
- Context-specific file sizes and quality settings
- Intelligent caching and incremental builds

## Image Contexts

The system recognizes these image contexts:

### 1. **Thumbnail** (`thumbnail`)
- **Usage**: Work portfolio thumbnails, grid layouts
- **Sizes**: 400px, 600px, 800px, 1200px, 1600px
- **Aspect Ratio**: 16:9 (preferred)
- **Max Width**: 1600px
- **Sizes Attribute**: `(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw`

### 2. **Regular** (`regular`)
- **Usage**: Standard work post content images
- **Sizes**: 400px, 600px, 800px, 1200px
- **Max Width**: 800px
- **Sizes Attribute**: `(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 800px`

### 3. **Mid** (`mid`)
- **Usage**: Work post images with `(mid)` modifier
- **Sizes**: 600px, 900px, 1200px, 1800px
- **Max Width**: 1200px
- **Sizes Attribute**: `(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 1200px`

### 4. **Wide** (`wide`)
- **Usage**: Work post images with `(wide)` modifier
- **Sizes**: 800px, 1200px, 1632px, 2400px
- **Max Width**: 1632px
- **Sizes Attribute**: `100vw`

### 5. **Side-by-Side** (`sideBySide`)
- **Usage**: Comparison layouts, smaller images
- **Sizes**: 320px, 480px, 600px, 900px
- **Max Width**: 600px
- **Sizes Attribute**: `(max-width: 640px) 100vw, (max-width: 1024px) 45vw, 400px`

### 6. **Carousel** (`carousel`)
- **Usage**: Image carousels and galleries
- **Sizes**: 400px, 600px, 800px, 1200px
- **Max Width**: 800px
- **Sizes Attribute**: `(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw`

## Hi-DPI Quality Settings

The system uses variable quality for different pixel densities:

- **1x (Standard)**: 90% quality
- **2x (Retina)**: 75% quality  
- **3x (High-DPI)**: 60% quality

This approach maintains visual quality while significantly reducing file sizes for high-density displays.

## File Naming Convention

Generated responsive variants follow this naming pattern:

```
original-name_[width]w_[dpr]x.[format]
```

Examples:
- `work_citysuper_cover_800w.avif` (800px width, 1x DPR, AVIF format)
- `work_citysuper_cover_800w_2x.avif` (800px base width, 2x DPR = 1600px actual, AVIF format)
- `thumb_elevate_600w_3x.webp` (600px base width, 3x DPR = 1800px actual, WebP format)

## Usage

### **Single Command Processing**

Run the comprehensive image processing command:

```bash
npm run img
```

This unified command will:
1. **Analyze** all images and detect their contexts
2. **Generate optimization recommendations** for oversized files
3. **Process** images with context-aware responsive variants
4. **Create** hi-DPI versions with variable quality
5. **Output** AVIF, WebP, and original formats

The command provides a complete analysis summary followed by optimized image generation, giving you both insights and results in one streamlined process.

### 3. **Automatic Usage in Components**

The system automatically applies context-aware optimization in:

- **MDXImage**: Detects wide/mid modifiers and applies appropriate context
- **WorkSection**: Uses thumbnail context for portfolio grid
- **WorkCarousel**: Uses carousel context for gallery images
- **SideBySideImages**: Uses sideBySide context for comparisons

## Best Practices

### 📸 **Image Preparation**
1. **Use descriptive filenames**: `thumb_projectname.jpg`, `work_projectname_cover.jpg`
2. **Maintain aspect ratios**: 16:9 for thumbnails, appropriate ratios for content
3. **Start with high quality**: The system will generate optimized variants
4. **Consider context**: Use wide/mid modifiers in alt text when needed

### 🎨 **Markdown Usage**
```markdown
![Regular image](/work/project-image.jpg)
![Wide image (wide)](/work/project-hero.jpg)
![Medium image (mid)](/work/project-detail.jpg)
```

### ⚡ **Performance Tips**
1. **Run analysis regularly**: Use `npm run analyze:images` to identify optimization opportunities
2. **Monitor file sizes**: The analysis report highlights oversized images
3. **Use appropriate contexts**: Don't use wide images for small content areas
4. **Leverage caching**: The system uses incremental builds to avoid reprocessing unchanged images

## Technical Implementation

### Context Detection Algorithm
1. **Filename patterns**: `thumb_*`, `*carousel*` → high confidence
2. **Dimensions + aspect ratio**: 16:9 + medium size → thumbnail (medium confidence)
3. **Size-based fallback**: Large width → wide, medium → mid, small → sideBySide

### Format Priority
1. **AVIF**: Best compression, modern browsers
2. **WebP**: Good compression, wide support
3. **Original**: Fallback for older browsers

### Srcset Generation
- **Width descriptors**: For responsive layouts (`400w, 600w, 800w`)
- **Density descriptors**: For fixed-size images (`1x, 2x, 3x`)
- **Automatic selection**: Based on context and usage patterns

## Monitoring and Optimization

### Regular Analysis
Run `npm run analyze:images` monthly to:
- Identify new optimization opportunities
- Check for oversized images
- Verify context detection accuracy
- Monitor file size trends

### Performance Metrics
- **File size reduction**: Typically 40-70% with AVIF/WebP
- **Load time improvement**: Faster loading with appropriate sizes
- **Visual quality**: Maintained across all device types
- **Cache efficiency**: Better hit rates with context-specific variants

## Migration from Previous System

The new system is backward compatible:
1. **Existing images**: Will be re-processed with new context detection
2. **Component updates**: Automatic - no manual changes needed
3. **Build process**: Enhanced optimization replaces previous simple resizing
4. **File cleanup**: Old variants can be removed after successful migration

## Troubleshooting

### Common Issues
1. **Context misdetection**: Add filename hints (`thumb_`, `carousel_`)
2. **Large file sizes**: Check analysis report for oversized originals
3. **Quality issues**: Verify source image quality and dimensions
4. **Missing variants**: Ensure optimization script completed successfully

### Debug Mode
Set `DEBUG=true` in the optimization script for detailed logging of context detection and variant generation.
